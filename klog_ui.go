package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/atotto/clipboard"
)

// handleLogStream processes incoming log entries and updates the display
func (m *KlogModel) handleLogStream(msg logStreamMsg) (tea.Model, tea.Cmd) {
	// Note: Log entries are already added to the buffer in readPodLogs
	// We just need to trigger a UI refresh here
	
	// Rebuild display lines if not paused
	if !m.paused {
		m.mu.Lock()
		m.rebuildDisplayLines()

		// Auto-scroll to bottom if we're near the end
		maxScroll := len(m.displayLines) - m.viewHeight
		if maxScroll > 0 && m.scrollOffset >= maxScroll-5 {
			m.scrollOffset = maxScroll
		}
		m.mu.Unlock()
	}

	// Continue listening for more log messages
	return m, m.listenForLogs()
}

// rebuildDisplayLines regenerates the display lines from the log buffer
// Note: This function assumes the caller has already acquired the mutex
func (m *KlogModel) rebuildDisplayLines() {
	m.displayLines = make([]string, 0, len(m.logBuffer))
	
	for _, entry := range m.logBuffer {
		// Get pod color
		podStream := m.podStreams[entry.PodName]
		var podColor lipgloss.Color = "242" // Default gray
		if podStream != nil {
			podColor = podStream.Color
		}
		
		// Format timestamp
		timeStr := entry.Timestamp.Format("15:04:05.000")
		
		// Create prefix with pod name and timestamp
		prefixStyle := lipgloss.NewStyle().Foreground(podColor).Bold(true)
		prefix := prefixStyle.Render(fmt.Sprintf("[%s %s]", timeStr, entry.PodName))
		
		// Handle line wrapping or truncation
		message := entry.Message
		if m.wrapMode {
			// Soft wrap: split long lines
			maxWidth := 120 // Adjust based on terminal width
			prefixLen := len(timeStr) + len(entry.PodName) + 4 // "[timestamp podname] "
			availableWidth := maxWidth - prefixLen
			
			if len(message) > availableWidth {
				// Split into multiple lines
				for len(message) > 0 {
					if len(message) <= availableWidth {
						m.displayLines = append(m.displayLines, prefix+" "+message)
						break
					}
					
					// Find a good break point (space, if possible)
					breakPoint := availableWidth
					for breakPoint > availableWidth*3/4 && breakPoint > 0 {
						if message[breakPoint] == ' ' {
							break
						}
						breakPoint--
					}
					if breakPoint <= availableWidth*3/4 {
						breakPoint = availableWidth
					}
					
					m.displayLines = append(m.displayLines, prefix+" "+message[:breakPoint])
					message = message[breakPoint:]
					
					// Subsequent lines get an indented prefix
					prefix = strings.Repeat(" ", prefixLen)
				}
			} else {
				m.displayLines = append(m.displayLines, prefix+" "+message)
			}
		} else {
			// Hard truncation
			maxWidth := 120 // Adjust based on terminal width
			prefixLen := len(timeStr) + len(entry.PodName) + 4
			availableWidth := maxWidth - prefixLen
			
			if len(message) > availableWidth {
				message = message[:availableWidth-1] + "…"
			}
			
			m.displayLines = append(m.displayLines, prefix+" "+message)
		}
	}
}

// performSearch searches for the current search term in the display lines
func (m *KlogModel) performSearch() {
	searchTerm := strings.ToLower(m.searchInput.Value())
	if searchTerm == "" {
		m.searchResults = nil
		return
	}
	
	m.searchResults = make([]int, 0)
	for i, line := range m.displayLines {
		if strings.Contains(strings.ToLower(line), searchTerm) {
			m.searchResults = append(m.searchResults, i)
		}
	}
	
	m.searchIndex = 0
	if len(m.searchResults) > 0 {
		m.scrollToLine(m.searchResults[0])
	}
}

// findNextMatch navigates to the next search match
func (m *KlogModel) findNextMatch() {
	if len(m.searchResults) == 0 {
		return
	}
	
	m.searchIndex = (m.searchIndex + 1) % len(m.searchResults)
	m.scrollToLine(m.searchResults[m.searchIndex])
}

// findPreviousMatch navigates to the previous search match
func (m *KlogModel) findPreviousMatch() {
	if len(m.searchResults) == 0 {
		return
	}
	
	m.searchIndex--
	if m.searchIndex < 0 {
		m.searchIndex = len(m.searchResults) - 1
	}
	m.scrollToLine(m.searchResults[m.searchIndex])
}

// scrollToLine scrolls the view to show the specified line
func (m *KlogModel) scrollToLine(lineIndex int) {
	// Center the line in the view
	center := m.viewHeight / 2
	m.scrollOffset = lineIndex - center
	
	// Clamp to valid range
	if m.scrollOffset < 0 {
		m.scrollOffset = 0
	}
	maxScroll := len(m.displayLines) - m.viewHeight
	if maxScroll > 0 && m.scrollOffset > maxScroll {
		m.scrollOffset = maxScroll
	}
}

// isSearchMatch checks if a line index is a search match
func (m *KlogModel) isSearchMatch(lineIndex int) bool {
	for _, match := range m.searchResults {
		if match == lineIndex {
			return true
		}
	}
	return false
}

// toggleSelection starts or ends selection mode
func (m *KlogModel) toggleSelection() {
	if !m.selectionMode {
		// Start selection
		m.selectionMode = true
		currentLine := m.scrollOffset + m.viewHeight/2 // Middle of visible area
		if currentLine >= len(m.displayLines) {
			currentLine = len(m.displayLines) - 1
		}
		m.selectionStart = currentLine
		m.selectionEnd = currentLine
	} else {
		// End selection
		m.selectionMode = false
	}
}

// isInSelection checks if a line index is within the current selection
func (m *KlogModel) isInSelection(lineIndex int) bool {
	if !m.selectionMode {
		return false
	}
	
	start := m.selectionStart
	end := m.selectionEnd
	if start > end {
		start, end = end, start
	}
	
	return lineIndex >= start && lineIndex <= end
}

// extendSelection extends the selection when using arrow keys
func (m *KlogModel) extendSelection(direction int) {
	if !m.selectionMode {
		return
	}
	
	m.selectionEnd += direction
	if m.selectionEnd < 0 {
		m.selectionEnd = 0
	}
	if m.selectionEnd >= len(m.displayLines) {
		m.selectionEnd = len(m.displayLines) - 1
	}
	
	// Update scroll to follow selection
	if direction > 0 && m.selectionEnd >= m.scrollOffset+m.viewHeight {
		m.scrollOffset = m.selectionEnd - m.viewHeight + 1
	} else if direction < 0 && m.selectionEnd < m.scrollOffset {
		m.scrollOffset = m.selectionEnd
	}
}

// copySelection copies the selected lines to clipboard
func (m *KlogModel) copySelection() tea.Cmd {
	return func() tea.Msg {
		if !m.selectionMode {
			return errorMsg("No selection to copy")
		}
		
		start := m.selectionStart
		end := m.selectionEnd
		if start > end {
			start, end = end, start
		}
		
		if start < 0 || end >= len(m.displayLines) {
			return errorMsg("Invalid selection range")
		}
		
		// Collect selected lines
		var lines []string
		for i := start; i <= end; i++ {
			lines = append(lines, m.displayLines[i])
		}
		
		text := strings.Join(lines, "\n")
		
		// Try to copy to clipboard
		err := clipboard.WriteAll(text)
		if err != nil {
			// Fallback: write to a temp file or stdout
			return m.fallbackCopy(text)
		}
		
		// Show success message temporarily
		m.lastError = fmt.Sprintf("Copied %d lines to clipboard", len(lines))
		go func() {
			time.Sleep(2 * time.Second)
			m.lastError = ""
		}()
		
		// End selection mode
		m.selectionMode = false
		
		return nil
	}
}

// fallbackCopy provides alternative copy methods when clipboard fails
func (m *KlogModel) fallbackCopy(text string) tea.Msg {
	// Try writing to a temp file
	tempFile, err := os.CreateTemp("", "klog-selection-*.txt")
	if err != nil {
		return errorMsg(fmt.Sprintf("Failed to copy to clipboard and create temp file: %v", err))
	}
	defer tempFile.Close()
	
	_, err = tempFile.WriteString(text)
	if err != nil {
		return errorMsg(fmt.Sprintf("Failed to write to temp file: %v", err))
	}
	
	m.lastError = fmt.Sprintf("Clipboard failed, selection saved to: %s", tempFile.Name())
	go func() {
		time.Sleep(5 * time.Second)
		m.lastError = ""
	}()
	
	return nil
}

// getCurrentFocusLine returns the currently focused line index
func (m *KlogModel) getCurrentFocusLine() int {
	if m.selectionMode {
		return m.selectionEnd
	}
	
	// Return middle of visible area
	focusLine := m.scrollOffset + m.viewHeight/2
	if focusLine >= len(m.displayLines) {
		focusLine = len(m.displayLines) - 1
	}
	if focusLine < 0 {
		focusLine = 0
	}
	
	return focusLine
}

// updateDisplayFromStreams periodically updates the display from streaming data
func (m *KlogModel) updateDisplayFromStreams() tea.Cmd {
	return tea.Tick(100*time.Millisecond, func(t time.Time) tea.Msg {
		// This will trigger a rebuild of display lines
		return logStreamMsg{entry: LogEntry{}}
	})
}

// renderPodList renders the pod list sidebar when enabled
func (m *KlogModel) renderPodList() string {
	if !m.showPodList {
		return ""
	}
	
	headerStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("39"))
	podStyle := lipgloss.NewStyle().PaddingLeft(1)
	
	s := headerStyle.Render("Active Pods:") + "\n"
	
	for _, podName := range m.activePods {
		podStream := m.podStreams[podName]
		if podStream != nil {
			status := "●" // Active indicator
			if !podStream.Active {
				status = "○" // Inactive indicator
			}
			
			colorStyle := lipgloss.NewStyle().Foreground(podStream.Color)
			line := fmt.Sprintf("%s %s (%d lines)", status, podName, len(podStream.Buffer))
			s += podStyle.Render(colorStyle.Render(line)) + "\n"
		}
	}
	
	return s
}

// Auto-update command to refresh the UI periodically
type tickMsg time.Time

func tickCmd() tea.Cmd {
	return tea.Tick(time.Second/10, func(t time.Time) tea.Msg {
		return tickMsg(t)
	})
} 