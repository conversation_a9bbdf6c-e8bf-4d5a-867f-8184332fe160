Index: go.mod
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>module dev-tools\r\n\r\ngo 1.23.0\r\n\r\ntoolchain go1.23.11\r\n\r\nrequire (\r\ngithub.com/charmbracelet/bubbles v0.21.0\r\ngithub.com/charmbracelet/bubbletea v1.3.6\r\ngithub.com/charmbracelet/lipgloss v1.1.0\r\ngithub.com/spf13/cobra v1.9.1\r\n)\r\n\r\nrequire (\r\ngithub.com/atotto/clipboard v0.1.4 // indirect\r\ngithub.com/aymanbagabas/go-osc52/v2 v2.0.1 // indirect\r\ngithub.com/charmbracelet/colorprofile v0.2.3-0.20250311203215-f60798e515dc // indirect\r\ngithub.com/charmbracelet/x/ansi v0.9.3 // indirect\r\ngithub.com/charmbracelet/x/cellbuf v0.0.13-0.20250311204145-2c3ea96c31dd // indirect\r\ngithub.com/charmbracelet/x/term v0.2.1 // indirect\r\ngithub.com/erikgeiser/coninput v0.0.0-20211004153227-1c3628e74d0f // indirect\r\ngithub.com/inconshreveable/mousetrap v1.1.0 // indirect\r\ngithub.com/lucasb-eyer/go-colorful v1.2.0 // indirect\r\ngithub.com/mattn/go-isatty v0.0.20 // indirect\r\ngithub.com/mattn/go-localereader v0.0.1 // indirect\r\ngithub.com/mattn/go-runewidth v0.0.16 // indirect\r\ngithub.com/muesli/ansi v0.0.0-20230316100256-276c6243b2f6 // indirect\r\ngithub.com/muesli/cancelreader v0.2.2 // indirect\r\ngithub.com/muesli/termenv v0.16.0 // indirect\r\ngithub.com/rivo/uniseg v0.4.7 // indirect\r\ngithub.com/spf13/pflag v1.0.6 // indirect\r\ngithub.com/xo/terminfo v0.0.0-20220910002029-abceb7e1c41e // indirect\r\ngolang.org/x/sync v0.15.0 // indirect\r\ngolang.org/x/sys v0.33.0 // indirect\r\ngolang.org/x/text v0.3.8 // indirect\r\n)
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/go.mod b/go.mod
--- a/go.mod	(revision ebdcb9cb6edeaa3b9cbfc0dd7fcba1e808ba3bce)
+++ b/go.mod	(date 1753492619928)
@@ -1,36 +1,73 @@
 module dev-tools
 
-go 1.23.0
+go 1.24.0
 
-toolchain go1.23.11
+toolchain go1.24.5
 
 require (
+github.com/atotto/clipboard v0.1.4
 github.com/charmbracelet/bubbles v0.21.0
 github.com/charmbracelet/bubbletea v1.3.6
 github.com/charmbracelet/lipgloss v1.1.0
 github.com/spf13/cobra v1.9.1
+gopkg.in/yaml.v3 v3.0.1
+k8s.io/api v0.33.3
+k8s.io/apimachinery v0.33.3
+k8s.io/client-go v0.33.3
 )
 
 require (
-github.com/atotto/clipboard v0.1.4 // indirect
 github.com/aymanbagabas/go-osc52/v2 v2.0.1 // indirect
 github.com/charmbracelet/colorprofile v0.2.3-0.20250311203215-f60798e515dc // indirect
 github.com/charmbracelet/x/ansi v0.9.3 // indirect
 github.com/charmbracelet/x/cellbuf v0.0.13-0.20250311204145-2c3ea96c31dd // indirect
 github.com/charmbracelet/x/term v0.2.1 // indirect
+github.com/davecgh/go-spew v1.1.1 // indirect
+github.com/emicklei/go-restful/v3 v3.11.0 // indirect
 github.com/erikgeiser/coninput v0.0.0-20211004153227-1c3628e74d0f // indirect
+github.com/fxamacker/cbor/v2 v2.7.0 // indirect
+github.com/go-logr/logr v1.4.2 // indirect
+github.com/go-openapi/jsonpointer v0.21.0 // indirect
+github.com/go-openapi/jsonreference v0.20.2 // indirect
+github.com/go-openapi/swag v0.23.0 // indirect
+github.com/gogo/protobuf v1.3.2 // indirect
+github.com/google/gnostic-models v0.6.9 // indirect
+github.com/google/go-cmp v0.7.0 // indirect
+github.com/google/uuid v1.6.0 // indirect
 github.com/inconshreveable/mousetrap v1.1.0 // indirect
+github.com/josharian/intern v1.0.0 // indirect
+github.com/json-iterator/go v1.1.12 // indirect
 github.com/lucasb-eyer/go-colorful v1.2.0 // indirect
+github.com/mailru/easyjson v0.7.7 // indirect
 github.com/mattn/go-isatty v0.0.20 // indirect
 github.com/mattn/go-localereader v0.0.1 // indirect
 github.com/mattn/go-runewidth v0.0.16 // indirect
+github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
+github.com/modern-go/reflect2 v1.0.2 // indirect
 github.com/muesli/ansi v0.0.0-20230316100256-276c6243b2f6 // indirect
 github.com/muesli/cancelreader v0.2.2 // indirect
 github.com/muesli/termenv v0.16.0 // indirect
+github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
+github.com/pkg/errors v0.9.1 // indirect
 github.com/rivo/uniseg v0.4.7 // indirect
 github.com/spf13/pflag v1.0.6 // indirect
+github.com/x448/float16 v0.8.4 // indirect
 github.com/xo/terminfo v0.0.0-20220910002029-abceb7e1c41e // indirect
+golang.org/x/net v0.38.0 // indirect
+golang.org/x/oauth2 v0.27.0 // indirect
 golang.org/x/sync v0.15.0 // indirect
 golang.org/x/sys v0.33.0 // indirect
-golang.org/x/text v0.3.8 // indirect
+golang.org/x/term v0.30.0 // indirect
+golang.org/x/text v0.23.0 // indirect
+golang.org/x/time v0.9.0 // indirect
+google.golang.org/protobuf v1.36.5 // indirect
+gopkg.in/evanphx/json-patch.v4 v4.12.0 // indirect
+gopkg.in/inf.v0 v0.9.1 // indirect
+k8s.io/klog/v2 v2.130.1 // indirect
+k8s.io/kube-openapi v0.0.0-20250318190949-c8a335a9a2ff // indirect
+k8s.io/utils v0.0.0-20241104100929-3ea5e8cea738 // indirect
+sigs.k8s.io/json v0.0.0-20241010143419-9aa6b5e7a4b3 // indirect
+sigs.k8s.io/randfill v1.0.0 // indirect
+sigs.k8s.io/structured-merge-diff/v4 v4.6.0 // indirect
+sigs.k8s.io/yaml v1.4.0 // indirect
 )
\ No newline at end of file
Index: klog.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/klog.go b/klog.go
new file mode 100644
--- /dev/null	(date 1753494513134)
+++ b/klog.go	(date 1753494513134)
@@ -0,0 +1,690 @@
+package main
+
+import (
+	"fmt"
+	"os"
+	"sync"
+	"time"
+
+	"github.com/charmbracelet/bubbles/textinput"
+	tea "github.com/charmbracelet/bubbletea"
+	"github.com/charmbracelet/lipgloss"
+	"k8s.io/client-go/kubernetes"
+)
+
+// LogEntry represents a single log line with metadata
+type LogEntry struct {
+	Timestamp time.Time
+	PodName   string
+	Message   string
+	Original  string
+}
+
+// PodLogStream manages streaming logs from a single pod
+type PodLogStream struct {
+	PodName   string
+	Buffer    []LogEntry
+	MaxBuffer int
+	Active    bool
+	Color     lipgloss.Color
+	mu        sync.RWMutex
+}
+
+// KlogModel represents the main klog TUI state
+type KlogModel struct {
+	// Kubernetes state
+	clientset     *kubernetes.Clientset
+	kubeconfig    string
+	context       string
+	namespace     string
+	deployment    string
+
+	// Pod management
+	podStreams    map[string]*PodLogStream
+	activePods    []string
+	maxPods       int
+
+	// Log streaming
+	logChan       chan LogEntry
+	program       *tea.Program
+
+	// UI state
+	mode          string // "select-context", "select-namespace", "select-deployment", "streaming"
+	
+	// Context selection
+	contexts      []string
+	contextCursor int
+	contextScrollOffset int
+	
+	// Namespace selection
+	namespaces    []string
+	namespaceCursor int
+	namespaceScrollOffset int
+	
+	// Deployment selection
+	deployments   []string
+	deploymentCursor int
+	deploymentScrollOffset int
+	
+	// Streaming view
+	logBuffer     []LogEntry
+	displayLines  []string
+	scrollOffset  int
+	viewHeight    int
+	
+	// Search
+	searchMode    bool
+	searchInput   textinput.Model
+	searchResults []int
+	searchIndex   int
+	
+	// Selection
+	selectionMode bool
+	selectionStart int
+	selectionEnd   int
+	
+	// Configuration
+	config        *KlogConfig
+	wrapMode      bool
+	paused        bool
+	showPodList   bool
+	
+	// Rate limiting for UI updates
+	lastUIUpdate  time.Time
+	
+	// Persistence
+	sessionDir    string
+	logFile       *os.File
+	
+	// Error handling
+	lastError     string
+	
+	mu            sync.RWMutex
+}
+
+// Pod colors for consistent identification
+var podColors = []lipgloss.Color{
+	"86", "118", "154", "190", "226", "220", "214", "208",
+	"202", "196", "161", "125", "89", "53", "17", "21",
+}
+
+func newKlog() (tea.Model, tea.Cmd) {
+	// Load configuration
+	config, _ := LoadKlogConfig()
+	
+	// Initialize search input
+	searchInput := textinput.New()
+	searchInput.Placeholder = "Search logs..."
+	searchInput.Width = 30
+	
+	model := &KlogModel{
+		mode:         "select-context",
+		config:       config,
+		maxPods:      config.MaxPods,
+		podStreams:   make(map[string]*PodLogStream),
+		logBuffer:    make([]LogEntry, 0, config.BufferSize),
+		displayLines: make([]string, 0),
+		searchInput:  searchInput,
+		viewHeight:   20,
+		wrapMode:     config.WrapMode,
+		paused:       false,
+		showPodList:  config.ShowPodList,
+		logChan:      make(chan LogEntry, 1000), // Buffered channel for log entries
+	}
+	
+	return model, tea.Batch(
+		model.loadKubeContexts(),
+		tea.EnterAltScreen,
+	)
+}
+
+func (m *KlogModel) Init() tea.Cmd {
+	return nil
+}
+
+// listenForLogs creates a command that listens for log entries from the channel
+func (m *KlogModel) listenForLogs() tea.Cmd {
+	return func() tea.Msg {
+		// Rate limit UI updates to 30 FPS max
+		minInterval := time.Millisecond * 33 // ~30 FPS
+		
+		select {
+		case entry := <-m.logChan:
+			// Only send update if enough time has passed since last update
+			now := time.Now()
+			if now.Sub(m.lastUIUpdate) >= minInterval {
+				m.lastUIUpdate = now
+				return logStreamMsg{entry: entry}
+			}
+			// If too soon, continue listening without UI update
+			return continueListeningMsg{}
+		case <-time.After(100 * time.Millisecond):
+			// Timeout, return a special message to continue listening
+			return continueListeningMsg{}
+		}
+	}
+}
+
+func (m *KlogModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
+	switch msg := msg.(type) {
+	case tea.WindowSizeMsg:
+		m.viewHeight = msg.Height - 10 // Reserve space for header/footer
+		return m, nil
+		
+	case tea.KeyMsg:
+		return m.handleKeyPress(msg)
+		
+	case kubeContextsMsg:
+		m.contexts = msg.contexts
+		// Apply config defaults if contexts are loaded
+		if m.config != nil {
+			m.ApplyConfig(m.config)
+		}
+		return m, nil
+		
+	case kubeNamespacesMsg:
+		m.namespaces = msg.namespaces
+		return m, nil
+		
+	case kubeDeploymentsMsg:
+		m.deployments = msg.deployments
+		return m, nil
+		
+	case logStreamMsg:
+		return m.handleLogStream(msg)
+		
+	case tickMsg:
+		// Periodic UI update for streaming mode
+		if m.mode == "streaming" && !m.paused {
+			m.mu.Lock()
+			m.rebuildDisplayLines()
+			m.mu.Unlock()
+			return m, tickCmd()
+		}
+		return m, tickCmd()
+		
+	case errorMsg:
+		m.lastError = string(msg)
+		return m, nil
+
+	case continueListeningMsg:
+		// Continue listening for log messages
+		return m, m.listenForLogs()
+	}
+	
+	// Handle search input updates
+	if m.searchMode {
+		var cmd tea.Cmd
+		m.searchInput, cmd = m.searchInput.Update(msg)
+		return m, cmd
+	}
+	
+	return m, nil
+}
+
+func (m *KlogModel) handleKeyPress(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
+	switch msg.String() {
+	case "ctrl+c", "esc":
+		if m.searchMode {
+			m.searchMode = false
+			m.searchInput.Blur()
+			return m, nil
+		}
+		if m.selectionMode {
+			m.selectionMode = false
+			return m, nil
+		}
+		if m.mode == "streaming" {
+			m.stopStreaming()
+		}
+		return selectorModel{}, nil
+		
+	case "q":
+		if m.searchMode || m.selectionMode {
+			return m, nil
+		}
+		if m.mode == "streaming" {
+			m.stopStreaming()
+		}
+		return selectorModel{}, nil
+	}
+	
+	switch m.mode {
+	case "select-context":
+		return m.handleContextSelection(msg)
+	case "select-namespace":
+		return m.handleNamespaceSelection(msg)
+	case "select-deployment":
+		return m.handleDeploymentSelection(msg)
+	case "streaming":
+		return m.handleStreamingKeys(msg)
+	}
+	
+	return m, nil
+}
+
+func (m *KlogModel) handleContextSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
+	switch msg.String() {
+	case "up", "k":
+		if m.contextCursor > 0 {
+			m.contextCursor--
+		}
+	case "down", "j":
+		if m.contextCursor < len(m.contexts)-1 {
+			m.contextCursor++
+		}
+	case "page_up":
+		pageSize := (m.viewHeight - 6) / 2
+		if pageSize < 1 {
+			pageSize = 1
+		}
+		m.contextCursor -= pageSize
+		if m.contextCursor < 0 {
+			m.contextCursor = 0
+		}
+	case "page_down":
+		pageSize := (m.viewHeight - 6) / 2
+		if pageSize < 1 {
+			pageSize = 1
+		}
+		m.contextCursor += pageSize
+		if m.contextCursor >= len(m.contexts) {
+			m.contextCursor = len(m.contexts) - 1
+		}
+	case "home":
+		m.contextCursor = 0
+	case "end":
+		m.contextCursor = len(m.contexts) - 1
+	case "enter":
+		if len(m.contexts) > 0 {
+			m.context = m.contexts[m.contextCursor]
+			m.mode = "select-namespace"
+			return m, m.loadNamespaces()
+		}
+	}
+	
+	// Update scroll offset
+	_, _, m.contextScrollOffset = m.calculateListViewport(len(m.contexts), m.contextCursor, m.contextScrollOffset)
+	
+	return m, nil
+}
+
+func (m *KlogModel) handleNamespaceSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
+	switch msg.String() {
+	case "up", "k":
+		if m.namespaceCursor > 0 {
+			m.namespaceCursor--
+		}
+	case "down", "j":
+		if m.namespaceCursor < len(m.namespaces)-1 {
+			m.namespaceCursor++
+		}
+	case "page_up":
+		pageSize := (m.viewHeight - 6) / 2
+		if pageSize < 1 {
+			pageSize = 1
+		}
+		m.namespaceCursor -= pageSize
+		if m.namespaceCursor < 0 {
+			m.namespaceCursor = 0
+		}
+	case "page_down":
+		pageSize := (m.viewHeight - 6) / 2
+		if pageSize < 1 {
+			pageSize = 1
+		}
+		m.namespaceCursor += pageSize
+		if m.namespaceCursor >= len(m.namespaces) {
+			m.namespaceCursor = len(m.namespaces) - 1
+		}
+	case "home":
+		m.namespaceCursor = 0
+	case "end":
+		m.namespaceCursor = len(m.namespaces) - 1
+	case "enter":
+		if len(m.namespaces) > 0 {
+			m.namespace = m.namespaces[m.namespaceCursor]
+			m.mode = "select-deployment"
+			return m, m.loadDeployments()
+		}
+	case "backspace":
+		m.mode = "select-context"
+	}
+	
+	// Update scroll offset
+	_, _, m.namespaceScrollOffset = m.calculateListViewport(len(m.namespaces), m.namespaceCursor, m.namespaceScrollOffset)
+	
+	return m, nil
+}
+
+func (m *KlogModel) handleDeploymentSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
+	switch msg.String() {
+	case "up", "k":
+		if m.deploymentCursor > 0 {
+			m.deploymentCursor--
+		}
+	case "down", "j":
+		if m.deploymentCursor < len(m.deployments)-1 {
+			m.deploymentCursor++
+		}
+	case "page_up":
+		pageSize := (m.viewHeight - 6) / 2
+		if pageSize < 1 {
+			pageSize = 1
+		}
+		m.deploymentCursor -= pageSize
+		if m.deploymentCursor < 0 {
+			m.deploymentCursor = 0
+		}
+	case "page_down":
+		pageSize := (m.viewHeight - 6) / 2
+		if pageSize < 1 {
+			pageSize = 1
+		}
+		m.deploymentCursor += pageSize
+		if m.deploymentCursor >= len(m.deployments) {
+			m.deploymentCursor = len(m.deployments) - 1
+		}
+	case "home":
+		m.deploymentCursor = 0
+	case "end":
+		m.deploymentCursor = len(m.deployments) - 1
+	case "enter":
+		if len(m.deployments) > 0 {
+			m.deployment = m.deployments[m.deploymentCursor]
+			m.mode = "streaming"
+			return m, tea.Batch(
+				m.initializeSession(),
+				m.startStreaming(),
+				m.listenForLogs(),
+				tickCmd(),
+			)
+		}
+	case "backspace":
+		m.mode = "select-namespace"
+	}
+	
+	// Update scroll offset
+	_, _, m.deploymentScrollOffset = m.calculateListViewport(len(m.deployments), m.deploymentCursor, m.deploymentScrollOffset)
+	
+	return m, nil
+}
+
+func (m *KlogModel) handleStreamingKeys(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
+	return m.handleStreamingKeysWithConfig(msg.String(), m.config)
+}
+
+func (m *KlogModel) View() string {
+	switch m.mode {
+	case "select-context":
+		return m.renderContextSelection()
+	case "select-namespace":
+		return m.renderNamespaceSelection()
+	case "select-deployment":
+		return m.renderDeploymentSelection()
+	case "streaming":
+		return m.renderStreaming()
+	default:
+		return "Loading..."
+	}
+}
+
+func (m *KlogModel) renderContextSelection() string {
+	headerStyle := lipgloss.NewStyle().
+		Bold(true).
+		Foreground(lipgloss.Color("39")).
+		MarginBottom(1)
+	
+	s := headerStyle.Render("🚢 Kubernetes Log Aggregator - Select Context") + "\n\n"
+	
+	if len(m.contexts) == 0 {
+		s += "Loading contexts...\n"
+		return s
+	}
+	
+	// Calculate viewport
+	visibleStart, visibleEnd, _ := m.calculateListViewport(len(m.contexts), m.contextCursor, m.contextScrollOffset)
+	
+	// Show scroll indicators
+	if visibleStart > 0 {
+		s += lipgloss.NewStyle().Faint(true).Render("  ↑ More items above...") + "\n"
+	}
+	
+	// Render visible items
+	for i := visibleStart; i < visibleEnd; i++ {
+		cursor := " "
+		style := lipgloss.NewStyle().PaddingLeft(2)
+		
+		if m.contextCursor == i {
+			cursor = ">"
+			style = style.Foreground(lipgloss.Color("170")).Bold(true).Background(lipgloss.Color("240"))
+		}
+		
+		s += style.Render(fmt.Sprintf("%s %s", cursor, m.contexts[i])) + "\n"
+	}
+	
+	// Show scroll indicators
+	if visibleEnd < len(m.contexts) {
+		s += lipgloss.NewStyle().Faint(true).Render("  ↓ More items below...") + "\n"
+	}
+	
+	// Add position indicator
+	totalInfo := fmt.Sprintf("(%d/%d)", m.contextCursor+1, len(m.contexts))
+	s += "\n" + lipgloss.NewStyle().Faint(true).Render("↑/k ↓/j navigate • PgUp/PgDn fast • Home/End jump • Enter select • q/esc quit " + totalInfo)
+	return s
+}
+
+func (m *KlogModel) renderNamespaceSelection() string {
+	headerStyle := lipgloss.NewStyle().
+		Bold(true).
+		Foreground(lipgloss.Color("39")).
+		MarginBottom(1)
+	
+	s := headerStyle.Render(fmt.Sprintf("🚢 Context: %s - Select Namespace", m.context)) + "\n\n"
+	
+	if len(m.namespaces) == 0 {
+		s += "Loading namespaces...\n"
+		return s
+	}
+	
+	// Calculate viewport
+	visibleStart, visibleEnd, _ := m.calculateListViewport(len(m.namespaces), m.namespaceCursor, m.namespaceScrollOffset)
+	
+	// Show scroll indicators
+	if visibleStart > 0 {
+		s += lipgloss.NewStyle().Faint(true).Render("  ↑ More items above...") + "\n"
+	}
+	
+	// Render visible items
+	for i := visibleStart; i < visibleEnd; i++ {
+		cursor := " "
+		style := lipgloss.NewStyle().PaddingLeft(2)
+		
+		if m.namespaceCursor == i {
+			cursor = ">"
+			style = style.Foreground(lipgloss.Color("170")).Bold(true).Background(lipgloss.Color("240"))
+		}
+		
+		s += style.Render(fmt.Sprintf("%s %s", cursor, m.namespaces[i])) + "\n"
+	}
+	
+	// Show scroll indicators
+	if visibleEnd < len(m.namespaces) {
+		s += lipgloss.NewStyle().Faint(true).Render("  ↓ More items below...") + "\n"
+	}
+	
+	// Add position indicator
+	totalInfo := fmt.Sprintf("(%d/%d)", m.namespaceCursor+1, len(m.namespaces))
+	s += "\n" + lipgloss.NewStyle().Faint(true).Render("↑/k ↓/j navigate • PgUp/PgDn fast • Home/End jump • Enter select • Backspace back • q/esc quit " + totalInfo)
+	return s
+}
+
+func (m *KlogModel) renderDeploymentSelection() string {
+	headerStyle := lipgloss.NewStyle().
+		Bold(true).
+		Foreground(lipgloss.Color("39")).
+		MarginBottom(1)
+	
+	s := headerStyle.Render(fmt.Sprintf("🚢 %s/%s - Select Deployment", m.context, m.namespace)) + "\n\n"
+	
+	if len(m.deployments) == 0 {
+		s += "Loading deployments...\n"
+		return s
+	}
+	
+	// Calculate viewport
+	visibleStart, visibleEnd, _ := m.calculateListViewport(len(m.deployments), m.deploymentCursor, m.deploymentScrollOffset)
+	
+	// Show scroll indicators
+	if visibleStart > 0 {
+		s += lipgloss.NewStyle().Faint(true).Render("  ↑ More items above...") + "\n"
+	}
+	
+	// Render visible items
+	for i := visibleStart; i < visibleEnd; i++ {
+		cursor := " "
+		style := lipgloss.NewStyle().PaddingLeft(2)
+		
+		if m.deploymentCursor == i {
+			cursor = ">"
+			style = style.Foreground(lipgloss.Color("170")).Bold(true).Background(lipgloss.Color("240"))
+		}
+		
+		s += style.Render(fmt.Sprintf("%s %s", cursor, m.deployments[i])) + "\n"
+	}
+	
+	// Show scroll indicators
+	if visibleEnd < len(m.deployments) {
+		s += lipgloss.NewStyle().Faint(true).Render("  ↓ More items below...") + "\n"
+	}
+	
+	// Add position indicator
+	totalInfo := fmt.Sprintf("(%d/%d)", m.deploymentCursor+1, len(m.deployments))
+	s += "\n" + lipgloss.NewStyle().Faint(true).Render("↑/k ↓/j navigate • PgUp/PgDn fast • Home/End jump • Enter select • Backspace back • q/esc quit " + totalInfo)
+	return s
+}
+
+func (m *KlogModel) renderStreaming() string {
+	headerStyle := lipgloss.NewStyle().
+		Bold(true).
+		Foreground(lipgloss.Color("39"))
+	
+	statusStyle := lipgloss.NewStyle().
+		Foreground(lipgloss.Color("242"))
+	
+	header := fmt.Sprintf("🚢 Streaming: %s/%s/%s", m.context, m.namespace, m.deployment)
+	if m.paused {
+		header += " [PAUSED]"
+	}
+	
+	status := fmt.Sprintf("Pods: %d | Lines: %d", len(m.activePods), len(m.logBuffer))
+	if m.searchMode {
+		status += " | Search: " + m.searchInput.View()
+	} else if len(m.searchResults) > 0 {
+		status += fmt.Sprintf(" | Matches: %d/%d", m.searchIndex+1, len(m.searchResults))
+	}
+	if m.selectionMode {
+		status += " | SELECTION MODE"
+	}
+	
+	s := headerStyle.Render(header) + "\n"
+	s += statusStyle.Render(status) + "\n\n"
+	
+	// Render log lines
+	start := m.scrollOffset
+	end := start + m.viewHeight
+	if end > len(m.displayLines) {
+		end = len(m.displayLines)
+	}
+	
+	for i := start; i < end; i++ {
+		line := m.displayLines[i]
+		
+		// Highlight search results
+		if m.isSearchMatch(i) {
+			line = lipgloss.NewStyle().Background(lipgloss.Color("220")).Render(line)
+		}
+		
+		// Highlight selection
+		if m.selectionMode && m.isInSelection(i) {
+			line = lipgloss.NewStyle().Background(lipgloss.Color("240")).Render(line)
+		}
+		
+		s += line + "\n"
+	}
+	
+	// Add some padding if we have fewer lines than view height
+	for i := end - start; i < m.viewHeight; i++ {
+		s += "\n"
+	}
+	
+	// Footer with help
+	helpStyle := lipgloss.NewStyle().Faint(true)
+	help := "/ search • w wrap • p pause • t pods • r refresh • ↑↓ scroll • Ctrl+↑↓ search nav • Ctrl+Space select • Ctrl+C copy • q quit"
+	s += helpStyle.Render(help)
+	
+	if m.lastError != "" {
+		errorStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("196"))
+		s += "\n" + errorStyle.Render("Error: "+m.lastError)
+	}
+	
+	return s
+}
+
+// calculateListViewport calculates the visible range for a scrollable list
+func (m *KlogModel) calculateListViewport(listLength, cursor, scrollOffset int) (visibleStart, visibleEnd, newScrollOffset int) {
+	maxViewportHeight := m.viewHeight - 6 // Reserve space for header, footer, etc.
+	if maxViewportHeight < 5 {
+		maxViewportHeight = 5 // Minimum viable height
+	}
+	
+	// Adjust scroll offset to keep cursor visible
+	if cursor < scrollOffset {
+		scrollOffset = cursor
+	} else if cursor >= scrollOffset+maxViewportHeight {
+		scrollOffset = cursor - maxViewportHeight + 1
+	}
+	
+	// Clamp scroll offset to valid range
+	if scrollOffset < 0 {
+		scrollOffset = 0
+	}
+	maxScrollOffset := listLength - maxViewportHeight
+	if maxScrollOffset < 0 {
+		maxScrollOffset = 0
+	}
+	if scrollOffset > maxScrollOffset {
+		scrollOffset = maxScrollOffset
+	}
+	
+	visibleStart = scrollOffset
+	visibleEnd = scrollOffset + maxViewportHeight
+	if visibleEnd > listLength {
+		visibleEnd = listLength
+	}
+	
+	return visibleStart, visibleEnd, scrollOffset
+}
+
+// Message types for async operations
+type kubeContextsMsg struct {
+	contexts []string
+}
+
+type kubeNamespacesMsg struct {
+	namespaces []string
+}
+
+type kubeDeploymentsMsg struct {
+	deployments []string
+}
+
+type logStreamMsg struct {
+	entry LogEntry
+}
+
+type errorMsg string
+
+type continueListeningMsg struct{}
+
+// Async command implementations will be in the next part...
\ No newline at end of file
Index: klog_kubernetes.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/klog_kubernetes.go b/klog_kubernetes.go
new file mode 100644
--- /dev/null	(date 1753494513134)
+++ b/klog_kubernetes.go	(date 1753494513134)
@@ -0,0 +1,371 @@
+package main
+
+import (
+	"bufio"
+	"context"
+	"fmt"
+	"io"
+	"os"
+	"path/filepath"
+	"sort"
+	"time"
+
+	tea "github.com/charmbracelet/bubbletea"
+	corev1 "k8s.io/api/core/v1"
+	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
+	"k8s.io/client-go/kubernetes"
+	"k8s.io/client-go/tools/clientcmd"
+)
+
+// loadKubeContexts returns a command that loads available Kubernetes contexts
+func (m *KlogModel) loadKubeContexts() tea.Cmd {
+	return func() tea.Msg {
+		loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
+		configOverrides := &clientcmd.ConfigOverrides{}
+		
+		kubeConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(loadingRules, configOverrides)
+		rawConfig, err := kubeConfig.RawConfig()
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to load kubeconfig: %v", err))
+		}
+		
+		var contexts []string
+		for name := range rawConfig.Contexts {
+			contexts = append(contexts, name)
+		}
+		sort.Strings(contexts)
+		
+		return kubeContextsMsg{contexts: contexts}
+	}
+}
+
+// loadNamespaces returns a command that loads namespaces for the selected context
+func (m *KlogModel) loadNamespaces() tea.Cmd {
+	return func() tea.Msg {
+		clientset, err := m.createKubernetesClient()
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to create Kubernetes client: %v", err))
+		}
+		
+		namespaceList, err := clientset.CoreV1().Namespaces().List(context.Background(), metav1.ListOptions{})
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to list namespaces: %v", err))
+		}
+		
+		var namespaces []string
+		for _, ns := range namespaceList.Items {
+			namespaces = append(namespaces, ns.Name)
+		}
+		sort.Strings(namespaces)
+		
+		return kubeNamespacesMsg{namespaces: namespaces}
+	}
+}
+
+// loadDeployments returns a command that loads deployments for the selected namespace
+func (m *KlogModel) loadDeployments() tea.Cmd {
+	return func() tea.Msg {
+		clientset, err := m.createKubernetesClient()
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to create Kubernetes client: %v", err))
+		}
+		
+		deploymentList, err := clientset.AppsV1().Deployments(m.namespace).List(context.Background(), metav1.ListOptions{})
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to list deployments: %v", err))
+		}
+		
+		var deployments []string
+		for _, deployment := range deploymentList.Items {
+			deployments = append(deployments, deployment.Name)
+		}
+		sort.Strings(deployments)
+		
+		return kubeDeploymentsMsg{deployments: deployments}
+	}
+}
+
+// createKubernetesClient creates a Kubernetes clientset for the selected context
+func (m *KlogModel) createKubernetesClient() (*kubernetes.Clientset, error) {
+	loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
+	configOverrides := &clientcmd.ConfigOverrides{
+		CurrentContext: m.context,
+	}
+	
+	kubeConfig := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(loadingRules, configOverrides)
+	config, err := kubeConfig.ClientConfig()
+	if err != nil {
+		return nil, err
+	}
+	
+	clientset, err := kubernetes.NewForConfig(config)
+	if err != nil {
+		return nil, err
+	}
+	
+	m.clientset = clientset
+	return clientset, nil
+}
+
+// initializeSession sets up the session directory and log file
+func (m *KlogModel) initializeSession() tea.Cmd {
+	return func() tea.Msg {
+		// Create session directory
+		homeDir, err := os.UserHomeDir()
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to get home directory: %v", err))
+		}
+		
+		sessionDir := filepath.Join(homeDir, "klog", "sessions")
+		if err := os.MkdirAll(sessionDir, 0755); err != nil {
+			return errorMsg(fmt.Sprintf("Failed to create session directory: %v", err))
+		}
+		
+		// Create log file
+		logFileName := fmt.Sprintf("%s_%s_%s.log", m.context, m.namespace, m.deployment)
+		logFilePath := filepath.Join(sessionDir, logFileName)
+		
+		logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to create log file: %v", err))
+		}
+		
+		m.sessionDir = sessionDir
+		m.logFile = logFile
+		
+		return nil
+	}
+}
+
+// startStreaming begins streaming logs from all pods in the deployment
+func (m *KlogModel) startStreaming() tea.Cmd {
+	return func() tea.Msg {
+		if m.clientset == nil {
+			return errorMsg("Kubernetes client not initialized")
+		}
+		
+		// Find pods for the deployment
+		pods, err := m.findPodsForDeployment()
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to find pods: %v", err))
+		}
+		
+		// Initialize pod streams
+		m.mu.Lock()
+		for i, pod := range pods {
+			if len(m.activePods) >= m.maxPods {
+				break
+			}
+			
+			colorIndex := i % len(podColors)
+			podStream := &PodLogStream{
+				PodName:   pod.Name,
+				Buffer:    make([]LogEntry, 0, 1000),
+				MaxBuffer: 1000,
+				Active:    true,
+				Color:     podColors[colorIndex],
+			}
+			
+			m.podStreams[pod.Name] = podStream
+			m.activePods = append(m.activePods, pod.Name)
+		}
+		m.mu.Unlock()
+		
+		// Start streaming from each pod
+		var cmds []tea.Cmd
+		for _, podName := range m.activePods {
+			cmds = append(cmds, m.streamPodLogs(podName))
+		}
+		
+		return tea.Batch(cmds...)
+	}
+}
+
+// findPodsForDeployment finds all pods belonging to the selected deployment
+func (m *KlogModel) findPodsForDeployment() ([]corev1.Pod, error) {
+	// Get the deployment to find its selector
+	deployment, err := m.clientset.AppsV1().Deployments(m.namespace).Get(
+		context.Background(), m.deployment, metav1.GetOptions{})
+	if err != nil {
+		return nil, fmt.Errorf("failed to get deployment: %v", err)
+	}
+	
+	// Convert selector to label selector string
+	selector := metav1.FormatLabelSelector(deployment.Spec.Selector)
+	
+	// Find pods with matching labels
+	podList, err := m.clientset.CoreV1().Pods(m.namespace).List(
+		context.Background(), metav1.ListOptions{LabelSelector: selector})
+	if err != nil {
+		return nil, fmt.Errorf("failed to list pods: %v", err)
+	}
+	
+	// Filter for running pods
+	var runningPods []corev1.Pod
+	for _, pod := range podList.Items {
+		if pod.Status.Phase == corev1.PodRunning {
+			runningPods = append(runningPods, pod)
+		}
+	}
+	
+	// Sort by creation time (newest first)
+	sort.Slice(runningPods, func(i, j int) bool {
+		return runningPods[i].CreationTimestamp.After(runningPods[j].CreationTimestamp.Time)
+	})
+	
+	return runningPods, nil
+}
+
+// streamPodLogs returns a command that streams logs from a specific pod
+func (m *KlogModel) streamPodLogs(podName string) tea.Cmd {
+	return func() tea.Msg {
+		req := m.clientset.CoreV1().Pods(m.namespace).GetLogs(podName, &corev1.PodLogOptions{
+			Follow:    true,
+			TailLines: &[]int64{100}[0], // Start with last 100 lines
+		})
+
+		stream, err := req.Stream(context.Background())
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to stream logs from pod %s: %v", podName, err))
+		}
+
+		// Start goroutine to read logs and return a command that will send log messages
+		return m.readPodLogsCmd(podName, stream)
+	}
+}
+
+// readPodLogsCmd returns a command that starts reading logs from a pod stream
+func (m *KlogModel) readPodLogsCmd(podName string, reader io.ReadCloser) tea.Cmd {
+	return func() tea.Msg {
+		// Start goroutine to read logs
+		go m.readPodLogs(podName, reader)
+		return nil
+	}
+}
+
+// readPodLogs reads logs from a pod stream in a goroutine
+func (m *KlogModel) readPodLogs(podName string, reader io.ReadCloser) {
+	defer reader.Close()
+
+	scanner := bufio.NewScanner(reader)
+	for scanner.Scan() {
+		podStream := m.podStreams[podName]
+		if podStream == nil || !podStream.Active {
+			continue
+		}
+
+		line := scanner.Text()
+		timestamp := time.Now()
+
+		entry := LogEntry{
+			Timestamp: timestamp,
+			PodName:   podName,
+			Message:   line,
+			Original:  line,
+		}
+
+		// Add to pod buffer
+		podStream.mu.Lock()
+		podStream.Buffer = append(podStream.Buffer, entry)
+		if len(podStream.Buffer) > podStream.MaxBuffer {
+			// Remove oldest entries
+			copy(podStream.Buffer, podStream.Buffer[1:])
+			podStream.Buffer = podStream.Buffer[:podStream.MaxBuffer]
+		}
+		podStream.mu.Unlock()
+
+		// Write to disk
+		if m.logFile != nil {
+			logLine := fmt.Sprintf("[%s] [%s] %s\n",
+				timestamp.Format("2006-01-02 15:04:05.000"), podName, line)
+			m.logFile.WriteString(logLine)
+			m.logFile.Sync()
+		}
+
+		// Add to main log buffer (only if not paused)
+		if !m.paused {
+			m.mu.Lock()
+			m.logBuffer = append(m.logBuffer, entry)
+			if len(m.logBuffer) > m.config.BufferSize {
+				// Remove oldest entries
+				copy(m.logBuffer, m.logBuffer[1:])
+				m.logBuffer = m.logBuffer[:m.config.BufferSize]
+			}
+			m.mu.Unlock()
+		}
+
+		// Send to channel for UI updates (non-blocking)
+		select {
+		case m.logChan <- entry:
+		default:
+			// Channel is full, skip this entry to avoid blocking
+		}
+	}
+
+	if err := scanner.Err(); err != nil {
+		// Set error in model for display
+		m.lastError = fmt.Sprintf("Error reading logs from pod %s: %v", podName, err)
+	}
+}
+
+// refreshPods discovers new pods and starts streaming from them
+func (m *KlogModel) refreshPods() tea.Cmd {
+	return func() tea.Msg {
+		pods, err := m.findPodsForDeployment()
+		if err != nil {
+			return errorMsg(fmt.Sprintf("Failed to refresh pods: %v", err))
+		}
+		
+		m.mu.Lock()
+		defer m.mu.Unlock()
+		
+		// Find new pods
+		existingPods := make(map[string]bool)
+		for _, podName := range m.activePods {
+			existingPods[podName] = true
+		}
+		
+		var newCmds []tea.Cmd
+		for i, pod := range pods {
+			if !existingPods[pod.Name] && len(m.activePods) < m.maxPods {
+				colorIndex := len(m.activePods) % len(podColors)
+				podStream := &PodLogStream{
+					PodName:   pod.Name,
+					Buffer:    make([]LogEntry, 0, 1000),
+					MaxBuffer: 1000,
+					Active:    true,
+					Color:     podColors[colorIndex],
+				}
+				
+				m.podStreams[pod.Name] = podStream
+				m.activePods = append(m.activePods, pod.Name)
+				newCmds = append(newCmds, m.streamPodLogs(pod.Name))
+			}
+			
+			if i >= m.maxPods {
+				break
+			}
+		}
+		
+		if len(newCmds) > 0 {
+			return tea.Batch(newCmds...)
+		}
+		
+		return nil
+	}
+}
+
+// stopStreaming stops all log streaming
+func (m *KlogModel) stopStreaming() {
+	m.mu.Lock()
+	defer m.mu.Unlock()
+	
+	for _, podStream := range m.podStreams {
+		podStream.Active = false
+	}
+	
+	if m.logFile != nil {
+		m.logFile.Close()
+		m.logFile = nil
+	}
+} 
\ No newline at end of file
Index: klog_ui.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/klog_ui.go b/klog_ui.go
new file mode 100644
--- /dev/null	(date 1753494513134)
+++ b/klog_ui.go	(date 1753494513134)
@@ -0,0 +1,362 @@
+package main
+
+import (
+	"fmt"
+	"os"
+	"strings"
+	"time"
+
+	tea "github.com/charmbracelet/bubbletea"
+	"github.com/charmbracelet/lipgloss"
+	"github.com/atotto/clipboard"
+)
+
+// handleLogStream processes incoming log entries and updates the display
+func (m *KlogModel) handleLogStream(msg logStreamMsg) (tea.Model, tea.Cmd) {
+	// Note: Log entries are already added to the buffer in readPodLogs
+	// We just need to trigger a UI refresh here
+	
+	// Rebuild display lines if not paused
+	if !m.paused {
+		m.mu.Lock()
+		m.rebuildDisplayLines()
+
+		// Auto-scroll to bottom if we're near the end
+		maxScroll := len(m.displayLines) - m.viewHeight
+		if maxScroll > 0 && m.scrollOffset >= maxScroll-5 {
+			m.scrollOffset = maxScroll
+		}
+		m.mu.Unlock()
+	}
+
+	// Continue listening for more log messages
+	return m, m.listenForLogs()
+}
+
+// rebuildDisplayLines regenerates the display lines from the log buffer
+// Note: This function assumes the caller has already acquired the mutex
+func (m *KlogModel) rebuildDisplayLines() {
+	m.displayLines = make([]string, 0, len(m.logBuffer))
+	
+	for _, entry := range m.logBuffer {
+		// Get pod color
+		podStream := m.podStreams[entry.PodName]
+		var podColor lipgloss.Color = "242" // Default gray
+		if podStream != nil {
+			podColor = podStream.Color
+		}
+		
+		// Format timestamp
+		timeStr := entry.Timestamp.Format("15:04:05.000")
+		
+		// Create prefix with pod name and timestamp
+		prefixStyle := lipgloss.NewStyle().Foreground(podColor).Bold(true)
+		prefix := prefixStyle.Render(fmt.Sprintf("[%s %s]", timeStr, entry.PodName))
+		
+		// Handle line wrapping or truncation
+		message := entry.Message
+		if m.wrapMode {
+			// Soft wrap: split long lines
+			maxWidth := 120 // Adjust based on terminal width
+			prefixLen := len(timeStr) + len(entry.PodName) + 4 // "[timestamp podname] "
+			availableWidth := maxWidth - prefixLen
+			
+			if len(message) > availableWidth {
+				// Split into multiple lines
+				for len(message) > 0 {
+					if len(message) <= availableWidth {
+						m.displayLines = append(m.displayLines, prefix+" "+message)
+						break
+					}
+					
+					// Find a good break point (space, if possible)
+					breakPoint := availableWidth
+					for breakPoint > availableWidth*3/4 && breakPoint > 0 {
+						if message[breakPoint] == ' ' {
+							break
+						}
+						breakPoint--
+					}
+					if breakPoint <= availableWidth*3/4 {
+						breakPoint = availableWidth
+					}
+					
+					m.displayLines = append(m.displayLines, prefix+" "+message[:breakPoint])
+					message = message[breakPoint:]
+					
+					// Subsequent lines get an indented prefix
+					prefix = strings.Repeat(" ", prefixLen)
+				}
+			} else {
+				m.displayLines = append(m.displayLines, prefix+" "+message)
+			}
+		} else {
+			// Hard truncation
+			maxWidth := 120 // Adjust based on terminal width
+			prefixLen := len(timeStr) + len(entry.PodName) + 4
+			availableWidth := maxWidth - prefixLen
+			
+			if len(message) > availableWidth {
+				message = message[:availableWidth-1] + "…"
+			}
+			
+			m.displayLines = append(m.displayLines, prefix+" "+message)
+		}
+	}
+}
+
+// performSearch searches for the current search term in the display lines
+func (m *KlogModel) performSearch() {
+	searchTerm := strings.ToLower(m.searchInput.Value())
+	if searchTerm == "" {
+		m.searchResults = nil
+		return
+	}
+	
+	m.searchResults = make([]int, 0)
+	for i, line := range m.displayLines {
+		if strings.Contains(strings.ToLower(line), searchTerm) {
+			m.searchResults = append(m.searchResults, i)
+		}
+	}
+	
+	m.searchIndex = 0
+	if len(m.searchResults) > 0 {
+		m.scrollToLine(m.searchResults[0])
+	}
+}
+
+// findNextMatch navigates to the next search match
+func (m *KlogModel) findNextMatch() {
+	if len(m.searchResults) == 0 {
+		return
+	}
+	
+	m.searchIndex = (m.searchIndex + 1) % len(m.searchResults)
+	m.scrollToLine(m.searchResults[m.searchIndex])
+}
+
+// findPreviousMatch navigates to the previous search match
+func (m *KlogModel) findPreviousMatch() {
+	if len(m.searchResults) == 0 {
+		return
+	}
+	
+	m.searchIndex--
+	if m.searchIndex < 0 {
+		m.searchIndex = len(m.searchResults) - 1
+	}
+	m.scrollToLine(m.searchResults[m.searchIndex])
+}
+
+// scrollToLine scrolls the view to show the specified line
+func (m *KlogModel) scrollToLine(lineIndex int) {
+	// Center the line in the view
+	center := m.viewHeight / 2
+	m.scrollOffset = lineIndex - center
+	
+	// Clamp to valid range
+	if m.scrollOffset < 0 {
+		m.scrollOffset = 0
+	}
+	maxScroll := len(m.displayLines) - m.viewHeight
+	if maxScroll > 0 && m.scrollOffset > maxScroll {
+		m.scrollOffset = maxScroll
+	}
+}
+
+// isSearchMatch checks if a line index is a search match
+func (m *KlogModel) isSearchMatch(lineIndex int) bool {
+	for _, match := range m.searchResults {
+		if match == lineIndex {
+			return true
+		}
+	}
+	return false
+}
+
+// toggleSelection starts or ends selection mode
+func (m *KlogModel) toggleSelection() {
+	if !m.selectionMode {
+		// Start selection
+		m.selectionMode = true
+		currentLine := m.scrollOffset + m.viewHeight/2 // Middle of visible area
+		if currentLine >= len(m.displayLines) {
+			currentLine = len(m.displayLines) - 1
+		}
+		m.selectionStart = currentLine
+		m.selectionEnd = currentLine
+	} else {
+		// End selection
+		m.selectionMode = false
+	}
+}
+
+// isInSelection checks if a line index is within the current selection
+func (m *KlogModel) isInSelection(lineIndex int) bool {
+	if !m.selectionMode {
+		return false
+	}
+	
+	start := m.selectionStart
+	end := m.selectionEnd
+	if start > end {
+		start, end = end, start
+	}
+	
+	return lineIndex >= start && lineIndex <= end
+}
+
+// extendSelection extends the selection when using arrow keys
+func (m *KlogModel) extendSelection(direction int) {
+	if !m.selectionMode {
+		return
+	}
+	
+	m.selectionEnd += direction
+	if m.selectionEnd < 0 {
+		m.selectionEnd = 0
+	}
+	if m.selectionEnd >= len(m.displayLines) {
+		m.selectionEnd = len(m.displayLines) - 1
+	}
+	
+	// Update scroll to follow selection
+	if direction > 0 && m.selectionEnd >= m.scrollOffset+m.viewHeight {
+		m.scrollOffset = m.selectionEnd - m.viewHeight + 1
+	} else if direction < 0 && m.selectionEnd < m.scrollOffset {
+		m.scrollOffset = m.selectionEnd
+	}
+}
+
+// copySelection copies the selected lines to clipboard
+func (m *KlogModel) copySelection() tea.Cmd {
+	return func() tea.Msg {
+		if !m.selectionMode {
+			return errorMsg("No selection to copy")
+		}
+		
+		start := m.selectionStart
+		end := m.selectionEnd
+		if start > end {
+			start, end = end, start
+		}
+		
+		if start < 0 || end >= len(m.displayLines) {
+			return errorMsg("Invalid selection range")
+		}
+		
+		// Collect selected lines
+		var lines []string
+		for i := start; i <= end; i++ {
+			lines = append(lines, m.displayLines[i])
+		}
+		
+		text := strings.Join(lines, "\n")
+		
+		// Try to copy to clipboard
+		err := clipboard.WriteAll(text)
+		if err != nil {
+			// Fallback: write to a temp file or stdout
+			return m.fallbackCopy(text)
+		}
+		
+		// Show success message temporarily
+		m.lastError = fmt.Sprintf("Copied %d lines to clipboard", len(lines))
+		go func() {
+			time.Sleep(2 * time.Second)
+			m.lastError = ""
+		}()
+		
+		// End selection mode
+		m.selectionMode = false
+		
+		return nil
+	}
+}
+
+// fallbackCopy provides alternative copy methods when clipboard fails
+func (m *KlogModel) fallbackCopy(text string) tea.Msg {
+	// Try writing to a temp file
+	tempFile, err := os.CreateTemp("", "klog-selection-*.txt")
+	if err != nil {
+		return errorMsg(fmt.Sprintf("Failed to copy to clipboard and create temp file: %v", err))
+	}
+	defer tempFile.Close()
+	
+	_, err = tempFile.WriteString(text)
+	if err != nil {
+		return errorMsg(fmt.Sprintf("Failed to write to temp file: %v", err))
+	}
+	
+	m.lastError = fmt.Sprintf("Clipboard failed, selection saved to: %s", tempFile.Name())
+	go func() {
+		time.Sleep(5 * time.Second)
+		m.lastError = ""
+	}()
+	
+	return nil
+}
+
+// getCurrentFocusLine returns the currently focused line index
+func (m *KlogModel) getCurrentFocusLine() int {
+	if m.selectionMode {
+		return m.selectionEnd
+	}
+	
+	// Return middle of visible area
+	focusLine := m.scrollOffset + m.viewHeight/2
+	if focusLine >= len(m.displayLines) {
+		focusLine = len(m.displayLines) - 1
+	}
+	if focusLine < 0 {
+		focusLine = 0
+	}
+	
+	return focusLine
+}
+
+// updateDisplayFromStreams periodically updates the display from streaming data
+func (m *KlogModel) updateDisplayFromStreams() tea.Cmd {
+	return tea.Tick(100*time.Millisecond, func(t time.Time) tea.Msg {
+		// This will trigger a rebuild of display lines
+		return logStreamMsg{entry: LogEntry{}}
+	})
+}
+
+// renderPodList renders the pod list sidebar when enabled
+func (m *KlogModel) renderPodList() string {
+	if !m.showPodList {
+		return ""
+	}
+	
+	headerStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("39"))
+	podStyle := lipgloss.NewStyle().PaddingLeft(1)
+	
+	s := headerStyle.Render("Active Pods:") + "\n"
+	
+	for _, podName := range m.activePods {
+		podStream := m.podStreams[podName]
+		if podStream != nil {
+			status := "●" // Active indicator
+			if !podStream.Active {
+				status = "○" // Inactive indicator
+			}
+			
+			colorStyle := lipgloss.NewStyle().Foreground(podStream.Color)
+			line := fmt.Sprintf("%s %s (%d lines)", status, podName, len(podStream.Buffer))
+			s += podStyle.Render(colorStyle.Render(line)) + "\n"
+		}
+	}
+	
+	return s
+}
+
+// Auto-update command to refresh the UI periodically
+type tickMsg time.Time
+
+func tickCmd() tea.Cmd {
+	return tea.Tick(time.Second/10, func(t time.Time) tea.Msg {
+		return tickMsg(t)
+	})
+} 
\ No newline at end of file
Index: klog_config.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/klog_config.go b/klog_config.go
new file mode 100644
--- /dev/null	(date 1753494513134)
+++ b/klog_config.go	(date 1753494513134)
@@ -0,0 +1,244 @@
+package main
+
+import (
+	"os"
+	"path/filepath"
+
+	tea "github.com/charmbracelet/bubbletea"
+	"gopkg.in/yaml.v3"
+)
+
+// KlogConfig holds configuration settings for the klog tool
+type KlogConfig struct {
+	// Default settings
+	DefaultContext   string `yaml:"default_context"`
+	DefaultNamespace string `yaml:"default_namespace"`
+	
+	// Performance settings
+	MaxPods       int `yaml:"max_pods"`
+	BufferSize    int `yaml:"buffer_size"`
+	PodBufferSize int `yaml:"pod_buffer_size"`
+	
+	// UI settings
+	WrapMode       bool `yaml:"wrap_mode"`
+	ShowPodList    bool `yaml:"show_pod_list"`
+	MaxLineWidth   int  `yaml:"max_line_width"`
+	ColorsEnabled  bool `yaml:"colors_enabled"`
+	
+	// Persistence settings
+	SessionDir    string `yaml:"session_dir"`
+	AutoSave      bool   `yaml:"auto_save"`
+	RotationSize  int64  `yaml:"rotation_size_mb"`
+	
+	// Keybindings
+	Keybindings KlogKeybindings `yaml:"keybindings"`
+}
+
+// KlogKeybindings defines customizable key mappings
+type KlogKeybindings struct {
+	Quit           []string `yaml:"quit"`
+	Search         []string `yaml:"search"`
+	NextMatch      []string `yaml:"next_match"`
+	PreviousMatch  []string `yaml:"previous_match"`
+	ToggleWrap     []string `yaml:"toggle_wrap"`
+	TogglePause    []string `yaml:"toggle_pause"`
+	TogglePodList  []string `yaml:"toggle_pod_list"`
+	RefreshPods    []string `yaml:"refresh_pods"`
+	StartSelection []string `yaml:"start_selection"`
+	CopySelection  []string `yaml:"copy_selection"`
+	ScrollUp       []string `yaml:"scroll_up"`
+	ScrollDown     []string `yaml:"scroll_down"`
+}
+
+// DefaultKlogConfig returns the default configuration
+func DefaultKlogConfig() *KlogConfig {
+	homeDir, _ := os.UserHomeDir()
+	
+	return &KlogConfig{
+		MaxPods:       100,
+		BufferSize:    10000,
+		PodBufferSize: 1000,
+		WrapMode:      false,
+		ShowPodList:   false,
+		MaxLineWidth:  120,
+		ColorsEnabled: true,
+		SessionDir:    filepath.Join(homeDir, "klog", "sessions"),
+		AutoSave:      true,
+		RotationSize:  100, // 100 MB
+		Keybindings: KlogKeybindings{
+			Quit:           []string{"q", "ctrl+c", "esc"},
+			Search:         []string{"/"},
+			NextMatch:      []string{"ctrl+down", "n"},
+			PreviousMatch:  []string{"ctrl+up", "N"},
+			ToggleWrap:     []string{"w"},
+			TogglePause:    []string{"p"},
+			TogglePodList:  []string{"t"},
+			RefreshPods:    []string{"r"},
+			StartSelection: []string{"ctrl+space"},
+			CopySelection:  []string{"ctrl+c"},
+			ScrollUp:       []string{"up", "k"},
+			ScrollDown:     []string{"down", "j"},
+		},
+	}
+}
+
+// LoadKlogConfig loads configuration from file or returns default
+func LoadKlogConfig() (*KlogConfig, error) {
+	homeDir, err := os.UserHomeDir()
+	if err != nil {
+		return DefaultKlogConfig(), nil
+	}
+	
+	configPath := filepath.Join(homeDir, ".klog.yaml")
+	
+	// If config file doesn't exist, create it with defaults
+	if _, err := os.Stat(configPath); os.IsNotExist(err) {
+		config := DefaultKlogConfig()
+		err = SaveKlogConfig(config)
+		if err != nil {
+			// If we can't save, just return defaults
+			return config, nil
+		}
+		return config, nil
+	}
+	
+	// Read existing config
+	data, err := os.ReadFile(configPath)
+	if err != nil {
+		return DefaultKlogConfig(), nil
+	}
+	
+	config := DefaultKlogConfig()
+	err = yaml.Unmarshal(data, config)
+	if err != nil {
+		// If config is corrupted, return defaults
+		return DefaultKlogConfig(), nil
+	}
+	
+	return config, nil
+}
+
+// SaveKlogConfig saves configuration to file
+func SaveKlogConfig(config *KlogConfig) error {
+	homeDir, err := os.UserHomeDir()
+	if err != nil {
+		return err
+	}
+	
+	configPath := filepath.Join(homeDir, ".klog.yaml")
+	
+	data, err := yaml.Marshal(config)
+	if err != nil {
+		return err
+	}
+	
+	return os.WriteFile(configPath, data, 0644)
+}
+
+// ApplyConfig applies configuration settings to the klog model
+func (m *KlogModel) ApplyConfig(config *KlogConfig) {
+	m.maxPods = config.MaxPods
+	m.wrapMode = config.WrapMode
+	m.showPodList = config.ShowPodList
+	
+	// Set default context and namespace if available
+	if config.DefaultContext != "" {
+		for i, ctx := range m.contexts {
+			if ctx == config.DefaultContext {
+				m.contextCursor = i
+				break
+			}
+		}
+	}
+	
+	// Update session directory
+	if config.SessionDir != "" {
+		m.sessionDir = config.SessionDir
+	}
+}
+
+// matchesKeybinding checks if a key matches any of the configured keybindings
+func matchesKeybinding(key string, bindings []string) bool {
+	for _, binding := range bindings {
+		if key == binding {
+			return true
+		}
+	}
+	return false
+}
+
+// UpdatedHandleStreamingKeys with configuration-aware keybindings
+func (m *KlogModel) handleStreamingKeysWithConfig(key string, config *KlogConfig) (tea.Model, tea.Cmd) {
+	if m.searchMode {
+		switch key {
+		case "enter":
+			m.performSearch()
+			m.searchMode = false
+			m.searchInput.Blur()
+		case "esc":
+			m.searchMode = false
+			m.searchInput.Blur()
+		}
+		return m, nil
+	}
+	
+	// Use configured keybindings
+	switch {
+	case matchesKeybinding(key, config.Keybindings.Search):
+		m.searchMode = true
+		m.searchInput.Focus()
+		return m, nil
+	case matchesKeybinding(key, config.Keybindings.ToggleWrap):
+		m.wrapMode = !m.wrapMode
+		m.mu.Lock()
+		m.rebuildDisplayLines()
+		m.mu.Unlock()
+	case matchesKeybinding(key, config.Keybindings.TogglePause):
+		m.paused = !m.paused
+	case matchesKeybinding(key, config.Keybindings.TogglePodList):
+		m.showPodList = !m.showPodList
+	case matchesKeybinding(key, config.Keybindings.RefreshPods):
+		return m, m.refreshPods()
+	case matchesKeybinding(key, config.Keybindings.ScrollUp):
+		if m.scrollOffset > 0 {
+			m.scrollOffset--
+		}
+	case matchesKeybinding(key, config.Keybindings.ScrollDown):
+		maxScroll := len(m.displayLines) - m.viewHeight
+		if maxScroll > 0 && m.scrollOffset < maxScroll {
+			m.scrollOffset++
+		}
+	case matchesKeybinding(key, config.Keybindings.NextMatch):
+		m.findNextMatch()
+	case matchesKeybinding(key, config.Keybindings.PreviousMatch):
+		m.findPreviousMatch()
+	case matchesKeybinding(key, config.Keybindings.StartSelection):
+		m.toggleSelection()
+	case matchesKeybinding(key, config.Keybindings.CopySelection):
+		if m.selectionMode {
+			return m, m.copySelection()
+		}
+	case key == "home":
+		m.scrollOffset = 0
+	case key == "end":
+		maxScroll := len(m.displayLines) - m.viewHeight
+		if maxScroll > 0 {
+			m.scrollOffset = maxScroll
+		}
+	case key == "page_up":
+		m.scrollOffset -= m.viewHeight
+		if m.scrollOffset < 0 {
+			m.scrollOffset = 0
+		}
+	case key == "page_down":
+		maxScroll := len(m.displayLines) - m.viewHeight
+		if maxScroll > 0 {
+			m.scrollOffset += m.viewHeight
+			if m.scrollOffset > maxScroll {
+				m.scrollOffset = maxScroll
+			}
+		}
+	}
+	
+	return m, nil
+} 
\ No newline at end of file
