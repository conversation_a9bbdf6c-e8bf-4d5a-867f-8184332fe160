package main

import (
	"os"
	"path/filepath"

	tea "github.com/charmbracelet/bubbletea"
	"gopkg.in/yaml.v3"
)

// KlogConfig holds configuration settings for the klog tool
type KlogConfig struct {
	// Default settings
	DefaultContext   string `yaml:"default_context"`
	DefaultNamespace string `yaml:"default_namespace"`
	
	// Performance settings
	MaxPods       int `yaml:"max_pods"`
	BufferSize    int `yaml:"buffer_size"`
	PodBufferSize int `yaml:"pod_buffer_size"`
	
	// UI settings
	WrapMode       bool `yaml:"wrap_mode"`
	ShowPodList    bool `yaml:"show_pod_list"`
	MaxLineWidth   int  `yaml:"max_line_width"`
	ColorsEnabled  bool `yaml:"colors_enabled"`
	
	// Persistence settings
	SessionDir    string `yaml:"session_dir"`
	AutoSave      bool   `yaml:"auto_save"`
	RotationSize  int64  `yaml:"rotation_size_mb"`
	
	// Keybindings
	Keybindings KlogKeybindings `yaml:"keybindings"`
}

// KlogKeybindings defines customizable key mappings
type KlogKeybindings struct {
	Quit           []string `yaml:"quit"`
	Search         []string `yaml:"search"`
	NextMatch      []string `yaml:"next_match"`
	PreviousMatch  []string `yaml:"previous_match"`
	ToggleWrap     []string `yaml:"toggle_wrap"`
	TogglePause    []string `yaml:"toggle_pause"`
	TogglePodList  []string `yaml:"toggle_pod_list"`
	RefreshPods    []string `yaml:"refresh_pods"`
	StartSelection []string `yaml:"start_selection"`
	CopySelection  []string `yaml:"copy_selection"`
	ScrollUp       []string `yaml:"scroll_up"`
	ScrollDown     []string `yaml:"scroll_down"`
}

// DefaultKlogConfig returns the default configuration
func DefaultKlogConfig() *KlogConfig {
	homeDir, _ := os.UserHomeDir()
	
	return &KlogConfig{
		MaxPods:       100,
		BufferSize:    10000,
		PodBufferSize: 1000,
		WrapMode:      false,
		ShowPodList:   false,
		MaxLineWidth:  120,
		ColorsEnabled: true,
		SessionDir:    filepath.Join(homeDir, "klog", "sessions"),
		AutoSave:      true,
		RotationSize:  100, // 100 MB
		Keybindings: KlogKeybindings{
			Quit:           []string{"q", "ctrl+c", "esc"},
			Search:         []string{"/"},
			NextMatch:      []string{"ctrl+down", "n"},
			PreviousMatch:  []string{"ctrl+up", "N"},
			ToggleWrap:     []string{"w"},
			TogglePause:    []string{"p"},
			TogglePodList:  []string{"t"},
			RefreshPods:    []string{"r"},
			StartSelection: []string{"ctrl+space"},
			CopySelection:  []string{"ctrl+c"},
			ScrollUp:       []string{"up", "k"},
			ScrollDown:     []string{"down", "j"},
		},
	}
}

// LoadKlogConfig loads configuration from file or returns default
func LoadKlogConfig() (*KlogConfig, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return DefaultKlogConfig(), nil
	}
	
	configPath := filepath.Join(homeDir, ".klog.yaml")
	
	// If config file doesn't exist, create it with defaults
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		config := DefaultKlogConfig()
		err = SaveKlogConfig(config)
		if err != nil {
			// If we can't save, just return defaults
			return config, nil
		}
		return config, nil
	}
	
	// Read existing config
	data, err := os.ReadFile(configPath)
	if err != nil {
		return DefaultKlogConfig(), nil
	}
	
	config := DefaultKlogConfig()
	err = yaml.Unmarshal(data, config)
	if err != nil {
		// If config is corrupted, return defaults
		return DefaultKlogConfig(), nil
	}
	
	return config, nil
}

// SaveKlogConfig saves configuration to file
func SaveKlogConfig(config *KlogConfig) error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return err
	}
	
	configPath := filepath.Join(homeDir, ".klog.yaml")
	
	data, err := yaml.Marshal(config)
	if err != nil {
		return err
	}
	
	return os.WriteFile(configPath, data, 0644)
}

// ApplyConfig applies configuration settings to the klog model
func (m *KlogModel) ApplyConfig(config *KlogConfig) {
	m.maxPods = config.MaxPods
	m.wrapMode = config.WrapMode
	m.showPodList = config.ShowPodList
	
	// Set default context and namespace if available
	if config.DefaultContext != "" {
		for i, ctx := range m.contexts {
			if ctx == config.DefaultContext {
				m.contextCursor = i
				break
			}
		}
	}
	
	// Update session directory
	if config.SessionDir != "" {
		m.sessionDir = config.SessionDir
	}
}

// matchesKeybinding checks if a key matches any of the configured keybindings
func matchesKeybinding(key string, bindings []string) bool {
	for _, binding := range bindings {
		if key == binding {
			return true
		}
	}
	return false
}

// UpdatedHandleStreamingKeys with configuration-aware keybindings
func (m *KlogModel) handleStreamingKeysWithConfig(key string, config *KlogConfig) (tea.Model, tea.Cmd) {
	if m.searchMode {
		switch key {
		case "enter":
			m.performSearch()
			m.searchMode = false
			m.searchInput.Blur()
		case "esc":
			m.searchMode = false
			m.searchInput.Blur()
		}
		return m, nil
	}
	
	// Use configured keybindings
	switch {
	case matchesKeybinding(key, config.Keybindings.Search):
		m.searchMode = true
		m.searchInput.Focus()
		return m, nil
	case matchesKeybinding(key, config.Keybindings.ToggleWrap):
		m.wrapMode = !m.wrapMode
		m.mu.Lock()
		m.rebuildDisplayLines()
		m.mu.Unlock()
	case matchesKeybinding(key, config.Keybindings.TogglePause):
		m.paused = !m.paused
	case matchesKeybinding(key, config.Keybindings.TogglePodList):
		m.showPodList = !m.showPodList
	case matchesKeybinding(key, config.Keybindings.RefreshPods):
		return m, m.refreshPods()
	case matchesKeybinding(key, config.Keybindings.ScrollUp):
		if m.scrollOffset > 0 {
			m.scrollOffset--
		}
	case matchesKeybinding(key, config.Keybindings.ScrollDown):
		maxScroll := len(m.displayLines) - m.viewHeight
		if maxScroll > 0 && m.scrollOffset < maxScroll {
			m.scrollOffset++
		}
	case matchesKeybinding(key, config.Keybindings.NextMatch):
		m.findNextMatch()
	case matchesKeybinding(key, config.Keybindings.PreviousMatch):
		m.findPreviousMatch()
	case matchesKeybinding(key, config.Keybindings.StartSelection):
		m.toggleSelection()
	case matchesKeybinding(key, config.Keybindings.CopySelection):
		if m.selectionMode {
			return m, m.copySelection()
		}
	case key == "home":
		m.scrollOffset = 0
	case key == "end":
		maxScroll := len(m.displayLines) - m.viewHeight
		if maxScroll > 0 {
			m.scrollOffset = maxScroll
		}
	case key == "page_up":
		m.scrollOffset -= m.viewHeight
		if m.scrollOffset < 0 {
			m.scrollOffset = 0
		}
	case key == "page_down":
		maxScroll := len(m.displayLines) - m.viewHeight
		if maxScroll > 0 {
			m.scrollOffset += m.viewHeight
			if m.scrollOffset > maxScroll {
				m.scrollOffset = maxScroll
			}
		}
	}
	
	return m, nil
} 