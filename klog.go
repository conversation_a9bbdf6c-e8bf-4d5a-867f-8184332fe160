package main

import (
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"k8s.io/client-go/kubernetes"
)

// LogEntry represents a single log line with metadata
type LogEntry struct {
	Timestamp time.Time
	PodName   string
	Message   string
	Original  string
}

// PodLogStream manages streaming logs from a single pod
type PodLogStream struct {
	PodName   string
	Buffer    []LogEntry
	MaxBuffer int
	Active    bool
	Color     lipgloss.Color
	mu        sync.RWMutex
}

// KlogModel represents the main klog TUI state
type KlogModel struct {
	// Kubernetes state
	clientset     *kubernetes.Clientset
	kubeconfig    string
	context       string
	namespace     string
	deployment    string

	// Pod management
	podStreams    map[string]*PodLogStream
	activePods    []string
	maxPods       int

	// Log streaming
	logChan       chan LogEntry
	program       *tea.Program

	// UI state
	mode          string // "select-context", "select-namespace", "select-deployment", "streaming"
	
	// Context selection
	contexts      []string
	contextCursor int
	contextScrollOffset int
	
	// Namespace selection
	namespaces    []string
	namespaceCursor int
	namespaceScrollOffset int
	
	// Deployment selection
	deployments   []string
	deploymentCursor int
	deploymentScrollOffset int
	
	// Streaming view
	logBuffer     []LogEntry
	displayLines  []string
	scrollOffset  int
	viewHeight    int
	
	// Search
	searchMode    bool
	searchInput   textinput.Model
	searchResults []int
	searchIndex   int
	
	// Selection
	selectionMode bool
	selectionStart int
	selectionEnd   int
	
	// Configuration
	config        *KlogConfig
	wrapMode      bool
	paused        bool
	showPodList   bool
	
	// Rate limiting for UI updates
	lastUIUpdate  time.Time
	
	// Persistence
	sessionDir    string
	logFile       *os.File
	
	// Error handling
	lastError     string
	
	mu            sync.RWMutex
}

// Pod colors for consistent identification
var podColors = []lipgloss.Color{
	"86", "118", "154", "190", "226", "220", "214", "208",
	"202", "196", "161", "125", "89", "53", "17", "21",
}

func newKlog() (tea.Model, tea.Cmd) {
	// Load configuration
	config, _ := LoadKlogConfig()
	
	// Initialize search input
	searchInput := textinput.New()
	searchInput.Placeholder = "Search logs..."
	searchInput.Width = 30
	
	model := &KlogModel{
		mode:         "select-context",
		config:       config,
		maxPods:      config.MaxPods,
		podStreams:   make(map[string]*PodLogStream),
		logBuffer:    make([]LogEntry, 0, config.BufferSize),
		displayLines: make([]string, 0),
		searchInput:  searchInput,
		viewHeight:   20,
		wrapMode:     config.WrapMode,
		paused:       false,
		showPodList:  config.ShowPodList,
		logChan:      make(chan LogEntry, 1000), // Buffered channel for log entries
	}
	
	return model, tea.Batch(
		model.loadKubeContexts(),
		tea.EnterAltScreen,
	)
}

func (m *KlogModel) Init() tea.Cmd {
	return nil
}

// listenForLogs creates a command that listens for log entries from the channel
func (m *KlogModel) listenForLogs() tea.Cmd {
	return func() tea.Msg {
		// Rate limit UI updates to 30 FPS max
		minInterval := time.Millisecond * 33 // ~30 FPS
		
		select {
		case entry := <-m.logChan:
			// Only send update if enough time has passed since last update
			now := time.Now()
			if now.Sub(m.lastUIUpdate) >= minInterval {
				m.lastUIUpdate = now
				return logStreamMsg{entry: entry}
			}
			// If too soon, continue listening without UI update
			return continueListeningMsg{}
		case <-time.After(100 * time.Millisecond):
			// Timeout, return a special message to continue listening
			return continueListeningMsg{}
		}
	}
}

func (m *KlogModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.viewHeight = msg.Height - 10 // Reserve space for header/footer
		return m, nil
		
	case tea.KeyMsg:
		return m.handleKeyPress(msg)
		
	case kubeContextsMsg:
		m.contexts = msg.contexts
		// Apply config defaults if contexts are loaded
		if m.config != nil {
			m.ApplyConfig(m.config)
		}
		return m, nil
		
	case kubeNamespacesMsg:
		m.namespaces = msg.namespaces
		return m, nil
		
	case kubeDeploymentsMsg:
		m.deployments = msg.deployments
		return m, nil
		
	case logStreamMsg:
		return m.handleLogStream(msg)
		
	case tickMsg:
		// Periodic UI update for streaming mode
		if m.mode == "streaming" && !m.paused {
			m.mu.Lock()
			m.rebuildDisplayLines()
			m.mu.Unlock()
			return m, tickCmd()
		}
		return m, tickCmd()
		
	case errorMsg:
		m.lastError = string(msg)
		return m, nil

	case continueListeningMsg:
		// Continue listening for log messages
		return m, m.listenForLogs()
	}
	
	// Handle search input updates
	if m.searchMode {
		var cmd tea.Cmd
		m.searchInput, cmd = m.searchInput.Update(msg)
		return m, cmd
	}
	
	return m, nil
}

func (m *KlogModel) handleKeyPress(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c", "esc":
		if m.searchMode {
			m.searchMode = false
			m.searchInput.Blur()
			return m, nil
		}
		if m.selectionMode {
			m.selectionMode = false
			return m, nil
		}
		if m.mode == "streaming" {
			m.stopStreaming()
		}
		return selectorModel{}, nil
		
	case "q":
		if m.searchMode || m.selectionMode {
			return m, nil
		}
		if m.mode == "streaming" {
			m.stopStreaming()
		}
		return selectorModel{}, nil
	}
	
	switch m.mode {
	case "select-context":
		return m.handleContextSelection(msg)
	case "select-namespace":
		return m.handleNamespaceSelection(msg)
	case "select-deployment":
		return m.handleDeploymentSelection(msg)
	case "streaming":
		return m.handleStreamingKeys(msg)
	}
	
	return m, nil
}

func (m *KlogModel) handleContextSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		if m.contextCursor > 0 {
			m.contextCursor--
		}
	case "down", "j":
		if m.contextCursor < len(m.contexts)-1 {
			m.contextCursor++
		}
	case "page_up":
		pageSize := (m.viewHeight - 6) / 2
		if pageSize < 1 {
			pageSize = 1
		}
		m.contextCursor -= pageSize
		if m.contextCursor < 0 {
			m.contextCursor = 0
		}
	case "page_down":
		pageSize := (m.viewHeight - 6) / 2
		if pageSize < 1 {
			pageSize = 1
		}
		m.contextCursor += pageSize
		if m.contextCursor >= len(m.contexts) {
			m.contextCursor = len(m.contexts) - 1
		}
	case "home":
		m.contextCursor = 0
	case "end":
		m.contextCursor = len(m.contexts) - 1
	case "enter":
		if len(m.contexts) > 0 {
			m.context = m.contexts[m.contextCursor]
			m.mode = "select-namespace"
			return m, m.loadNamespaces()
		}
	}
	
	// Update scroll offset
	_, _, m.contextScrollOffset = m.calculateListViewport(len(m.contexts), m.contextCursor, m.contextScrollOffset)
	
	return m, nil
}

func (m *KlogModel) handleNamespaceSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		if m.namespaceCursor > 0 {
			m.namespaceCursor--
		}
	case "down", "j":
		if m.namespaceCursor < len(m.namespaces)-1 {
			m.namespaceCursor++
		}
	case "page_up":
		pageSize := (m.viewHeight - 6) / 2
		if pageSize < 1 {
			pageSize = 1
		}
		m.namespaceCursor -= pageSize
		if m.namespaceCursor < 0 {
			m.namespaceCursor = 0
		}
	case "page_down":
		pageSize := (m.viewHeight - 6) / 2
		if pageSize < 1 {
			pageSize = 1
		}
		m.namespaceCursor += pageSize
		if m.namespaceCursor >= len(m.namespaces) {
			m.namespaceCursor = len(m.namespaces) - 1
		}
	case "home":
		m.namespaceCursor = 0
	case "end":
		m.namespaceCursor = len(m.namespaces) - 1
	case "enter":
		if len(m.namespaces) > 0 {
			m.namespace = m.namespaces[m.namespaceCursor]
			m.mode = "select-deployment"
			return m, m.loadDeployments()
		}
	case "backspace":
		m.mode = "select-context"
	}
	
	// Update scroll offset
	_, _, m.namespaceScrollOffset = m.calculateListViewport(len(m.namespaces), m.namespaceCursor, m.namespaceScrollOffset)
	
	return m, nil
}

func (m *KlogModel) handleDeploymentSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		if m.deploymentCursor > 0 {
			m.deploymentCursor--
		}
	case "down", "j":
		if m.deploymentCursor < len(m.deployments)-1 {
			m.deploymentCursor++
		}
	case "page_up":
		pageSize := (m.viewHeight - 6) / 2
		if pageSize < 1 {
			pageSize = 1
		}
		m.deploymentCursor -= pageSize
		if m.deploymentCursor < 0 {
			m.deploymentCursor = 0
		}
	case "page_down":
		pageSize := (m.viewHeight - 6) / 2
		if pageSize < 1 {
			pageSize = 1
		}
		m.deploymentCursor += pageSize
		if m.deploymentCursor >= len(m.deployments) {
			m.deploymentCursor = len(m.deployments) - 1
		}
	case "home":
		m.deploymentCursor = 0
	case "end":
		m.deploymentCursor = len(m.deployments) - 1
	case "enter":
		if len(m.deployments) > 0 {
			m.deployment = m.deployments[m.deploymentCursor]
			m.mode = "streaming"
			return m, tea.Batch(
				m.initializeSession(),
				m.startStreaming(),
				m.listenForLogs(),
				tickCmd(),
			)
		}
	case "backspace":
		m.mode = "select-namespace"
	}
	
	// Update scroll offset
	_, _, m.deploymentScrollOffset = m.calculateListViewport(len(m.deployments), m.deploymentCursor, m.deploymentScrollOffset)
	
	return m, nil
}

func (m *KlogModel) handleStreamingKeys(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	return m.handleStreamingKeysWithConfig(msg.String(), m.config)
}

func (m *KlogModel) View() string {
	switch m.mode {
	case "select-context":
		return m.renderContextSelection()
	case "select-namespace":
		return m.renderNamespaceSelection()
	case "select-deployment":
		return m.renderDeploymentSelection()
	case "streaming":
		return m.renderStreaming()
	default:
		return "Loading..."
	}
}

func (m *KlogModel) renderContextSelection() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		MarginBottom(1)
	
	s := headerStyle.Render("🚢 Kubernetes Log Aggregator - Select Context") + "\n\n"
	
	if len(m.contexts) == 0 {
		s += "Loading contexts...\n"
		return s
	}
	
	// Calculate viewport
	visibleStart, visibleEnd, _ := m.calculateListViewport(len(m.contexts), m.contextCursor, m.contextScrollOffset)
	
	// Show scroll indicators
	if visibleStart > 0 {
		s += lipgloss.NewStyle().Faint(true).Render("  ↑ More items above...") + "\n"
	}
	
	// Render visible items
	for i := visibleStart; i < visibleEnd; i++ {
		cursor := " "
		style := lipgloss.NewStyle().PaddingLeft(2)
		
		if m.contextCursor == i {
			cursor = ">"
			style = style.Foreground(lipgloss.Color("170")).Bold(true).Background(lipgloss.Color("240"))
		}
		
		s += style.Render(fmt.Sprintf("%s %s", cursor, m.contexts[i])) + "\n"
	}
	
	// Show scroll indicators
	if visibleEnd < len(m.contexts) {
		s += lipgloss.NewStyle().Faint(true).Render("  ↓ More items below...") + "\n"
	}
	
	// Add position indicator
	totalInfo := fmt.Sprintf("(%d/%d)", m.contextCursor+1, len(m.contexts))
	s += "\n" + lipgloss.NewStyle().Faint(true).Render("↑/k ↓/j navigate • PgUp/PgDn fast • Home/End jump • Enter select • q/esc quit " + totalInfo)
	return s
}

func (m *KlogModel) renderNamespaceSelection() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		MarginBottom(1)
	
	s := headerStyle.Render(fmt.Sprintf("🚢 Context: %s - Select Namespace", m.context)) + "\n\n"
	
	if len(m.namespaces) == 0 {
		s += "Loading namespaces...\n"
		return s
	}
	
	// Calculate viewport
	visibleStart, visibleEnd, _ := m.calculateListViewport(len(m.namespaces), m.namespaceCursor, m.namespaceScrollOffset)
	
	// Show scroll indicators
	if visibleStart > 0 {
		s += lipgloss.NewStyle().Faint(true).Render("  ↑ More items above...") + "\n"
	}
	
	// Render visible items
	for i := visibleStart; i < visibleEnd; i++ {
		cursor := " "
		style := lipgloss.NewStyle().PaddingLeft(2)
		
		if m.namespaceCursor == i {
			cursor = ">"
			style = style.Foreground(lipgloss.Color("170")).Bold(true).Background(lipgloss.Color("240"))
		}
		
		s += style.Render(fmt.Sprintf("%s %s", cursor, m.namespaces[i])) + "\n"
	}
	
	// Show scroll indicators
	if visibleEnd < len(m.namespaces) {
		s += lipgloss.NewStyle().Faint(true).Render("  ↓ More items below...") + "\n"
	}
	
	// Add position indicator
	totalInfo := fmt.Sprintf("(%d/%d)", m.namespaceCursor+1, len(m.namespaces))
	s += "\n" + lipgloss.NewStyle().Faint(true).Render("↑/k ↓/j navigate • PgUp/PgDn fast • Home/End jump • Enter select • Backspace back • q/esc quit " + totalInfo)
	return s
}

func (m *KlogModel) renderDeploymentSelection() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		MarginBottom(1)
	
	s := headerStyle.Render(fmt.Sprintf("🚢 %s/%s - Select Deployment", m.context, m.namespace)) + "\n\n"
	
	if len(m.deployments) == 0 {
		s += "Loading deployments...\n"
		return s
	}
	
	// Calculate viewport
	visibleStart, visibleEnd, _ := m.calculateListViewport(len(m.deployments), m.deploymentCursor, m.deploymentScrollOffset)
	
	// Show scroll indicators
	if visibleStart > 0 {
		s += lipgloss.NewStyle().Faint(true).Render("  ↑ More items above...") + "\n"
	}
	
	// Render visible items
	for i := visibleStart; i < visibleEnd; i++ {
		cursor := " "
		style := lipgloss.NewStyle().PaddingLeft(2)
		
		if m.deploymentCursor == i {
			cursor = ">"
			style = style.Foreground(lipgloss.Color("170")).Bold(true).Background(lipgloss.Color("240"))
		}
		
		s += style.Render(fmt.Sprintf("%s %s", cursor, m.deployments[i])) + "\n"
	}
	
	// Show scroll indicators
	if visibleEnd < len(m.deployments) {
		s += lipgloss.NewStyle().Faint(true).Render("  ↓ More items below...") + "\n"
	}
	
	// Add position indicator
	totalInfo := fmt.Sprintf("(%d/%d)", m.deploymentCursor+1, len(m.deployments))
	s += "\n" + lipgloss.NewStyle().Faint(true).Render("↑/k ↓/j navigate • PgUp/PgDn fast • Home/End jump • Enter select • Backspace back • q/esc quit " + totalInfo)
	return s
}

func (m *KlogModel) renderStreaming() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39"))
	
	statusStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("242"))
	
	header := fmt.Sprintf("🚢 Streaming: %s/%s/%s", m.context, m.namespace, m.deployment)
	if m.paused {
		header += " [PAUSED]"
	}
	
	status := fmt.Sprintf("Pods: %d | Lines: %d", len(m.activePods), len(m.logBuffer))
	if m.searchMode {
		status += " | Search: " + m.searchInput.View()
	} else if len(m.searchResults) > 0 {
		status += fmt.Sprintf(" | Matches: %d/%d", m.searchIndex+1, len(m.searchResults))
	}
	if m.selectionMode {
		status += " | SELECTION MODE"
	}
	
	s := headerStyle.Render(header) + "\n"
	s += statusStyle.Render(status) + "\n\n"
	
	// Render log lines
	start := m.scrollOffset
	end := start + m.viewHeight
	if end > len(m.displayLines) {
		end = len(m.displayLines)
	}
	
	for i := start; i < end; i++ {
		line := m.displayLines[i]
		
		// Highlight search results
		if m.isSearchMatch(i) {
			line = lipgloss.NewStyle().Background(lipgloss.Color("220")).Render(line)
		}
		
		// Highlight selection
		if m.selectionMode && m.isInSelection(i) {
			line = lipgloss.NewStyle().Background(lipgloss.Color("240")).Render(line)
		}
		
		s += line + "\n"
	}
	
	// Add some padding if we have fewer lines than view height
	for i := end - start; i < m.viewHeight; i++ {
		s += "\n"
	}
	
	// Footer with help
	helpStyle := lipgloss.NewStyle().Faint(true)
	help := "/ search • w wrap • p pause • t pods • r refresh • ↑↓ scroll • Ctrl+↑↓ search nav • Ctrl+Space select • Ctrl+C copy • q quit"
	s += helpStyle.Render(help)
	
	if m.lastError != "" {
		errorStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("196"))
		s += "\n" + errorStyle.Render("Error: "+m.lastError)
	}
	
	return s
}

// calculateListViewport calculates the visible range for a scrollable list
func (m *KlogModel) calculateListViewport(listLength, cursor, scrollOffset int) (visibleStart, visibleEnd, newScrollOffset int) {
	maxViewportHeight := m.viewHeight - 6 // Reserve space for header, footer, etc.
	if maxViewportHeight < 5 {
		maxViewportHeight = 5 // Minimum viable height
	}
	
	// Adjust scroll offset to keep cursor visible
	if cursor < scrollOffset {
		scrollOffset = cursor
	} else if cursor >= scrollOffset+maxViewportHeight {
		scrollOffset = cursor - maxViewportHeight + 1
	}
	
	// Clamp scroll offset to valid range
	if scrollOffset < 0 {
		scrollOffset = 0
	}
	maxScrollOffset := listLength - maxViewportHeight
	if maxScrollOffset < 0 {
		maxScrollOffset = 0
	}
	if scrollOffset > maxScrollOffset {
		scrollOffset = maxScrollOffset
	}
	
	visibleStart = scrollOffset
	visibleEnd = scrollOffset + maxViewportHeight
	if visibleEnd > listLength {
		visibleEnd = listLength
	}
	
	return visibleStart, visibleEnd, scrollOffset
}

// Message types for async operations
type kubeContextsMsg struct {
	contexts []string
}

type kubeNamespacesMsg struct {
	namespaces []string
}

type kubeDeploymentsMsg struct {
	deployments []string
}

type logStreamMsg struct {
	entry LogEntry
}

type errorMsg string

type continueListeningMsg struct{}

// Async command implementations will be in the next part...