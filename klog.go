package main

import (
	"context"
	"fmt"
	"time"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// UIState represents the current state of the klog tool
type UIState int

const (
	StateContextSelection UIState = iota
	StateNamespaceSelection
	StateDeploymentSelection
	StateLogStreaming
	StateError
)

// KlogModel represents the main model for the Kubernetes log tool
type KlogModel struct {
	state       UIState
	k8sClient   kubernetes.Interface

	// Selection data
	contexts    []string
	namespaces  []string
	deployments []string

	// Current selections
	selectedContext    string
	selectedNamespace  string
	selectedDeployment string

	// UI state
	cursor      int
	loading     bool
	errorMsg    string

	// Search input
	searchInput textinput.Model
	searchMode  bool

	// Log data (simplified for initial implementation)
	logLines    []LogLine
	scrollOffset int
	wrapMode     bool
	paused       bool
}

// LogLine represents a single log line with metadata
type LogLine struct {
	Timestamp time.Time
	PodName   string
	Content   string
	Color     lipgloss.Color
}

// newKlog creates a new Kubernetes log tool instance
func newKlog() (tea.Model, tea.Cmd) {
	// Initialize search input
	searchInput := textinput.New()
	searchInput.Placeholder = "Search logs..."
	searchInput.CharLimit = 100
	searchInput.Width = 50

	model := &KlogModel{
		state:       StateContextSelection,
		cursor:      0,
		searchInput: searchInput,
		logLines:    make([]LogLine, 0),
	}

	return model, tea.Batch(
		model.loadContexts(),
		textinput.Blink,
	)
}

func (m *KlogModel) Init() tea.Cmd {
	return tea.Batch(
		m.loadContexts(),
		textinput.Blink,
	)
}

func (m *KlogModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		// Global keybindings
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "esc":
			return m.handleEscape()
		}

		// State-specific keybindings
		switch m.state {
		case StateContextSelection:
			return m.updateContextSelection(msg)
		case StateNamespaceSelection:
			return m.updateNamespaceSelection(msg)
		case StateDeploymentSelection:
			return m.updateDeploymentSelection(msg)
		case StateLogStreaming:
			return m.updateLogStreaming(msg)
		case StateError:
			return m.updateError(msg)
		}

	case ContextsLoadedMsg:
		m.contexts = msg.Contexts
		m.loading = false
		if len(m.contexts) == 0 {
			m.state = StateError
			m.errorMsg = "No Kubernetes contexts found"
		}

	case NamespacesLoadedMsg:
		m.namespaces = msg.Namespaces
		m.loading = false
		if len(m.namespaces) == 0 {
			m.state = StateError
			m.errorMsg = "No namespaces found in context"
		}

	case DeploymentsLoadedMsg:
		m.deployments = msg.Deployments
		m.loading = false
		if len(m.deployments) == 0 {
			m.state = StateError
			m.errorMsg = "No deployments found in namespace"
		}

	case ErrorMsg:
		m.state = StateError
		m.errorMsg = msg.Error
		m.loading = false

	case LogsReceivedMsg:
		m.logLines = append(m.logLines, msg.Lines...)
	}

	// Update search input if in search mode
	if m.searchMode && m.state == StateLogStreaming {
		m.searchInput, cmd = m.searchInput.Update(msg)
		cmds = append(cmds, cmd)
	}

	return m, tea.Batch(cmds...)
}

func (m *KlogModel) View() string {
	switch m.state {
	case StateContextSelection:
		return m.viewContextSelection()
	case StateNamespaceSelection:
		return m.viewNamespaceSelection()
	case StateDeploymentSelection:
		return m.viewDeploymentSelection()
	case StateLogStreaming:
		return m.viewLogStreaming()
	case StateError:
		return m.viewError()
	default:
		return "Unknown state"
	}
}

// Message types for async operations
type ContextsLoadedMsg struct {
	Contexts []string
}

type NamespacesLoadedMsg struct {
	Namespaces []string
}

type DeploymentsLoadedMsg struct {
	Deployments []string
}

type ErrorMsg struct {
	Error string
}

// loadContexts loads available Kubernetes contexts
func (m *KlogModel) loadContexts() tea.Cmd {
	return func() tea.Msg {
		config, err := clientcmd.NewDefaultClientConfigLoadingRules().Load()
		if err != nil {
			return ErrorMsg{Error: fmt.Sprintf("Failed to load kubeconfig: %v", err)}
		}

		contexts := make([]string, 0, len(config.Contexts))
		for name := range config.Contexts {
			contexts = append(contexts, name)
		}

		return ContextsLoadedMsg{Contexts: contexts}
	}
}

// loadNamespaces loads namespaces for the selected context
func (m *KlogModel) loadNamespaces() tea.Cmd {
	return func() tea.Msg {
		// Create client for selected context
		config, err := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			clientcmd.NewDefaultClientConfigLoadingRules(),
			&clientcmd.ConfigOverrides{CurrentContext: m.selectedContext},
		).ClientConfig()
		if err != nil {
			return ErrorMsg{Error: fmt.Sprintf("Failed to create client config: %v", err)}
		}

		clientset, err := kubernetes.NewForConfig(config)
		if err != nil {
			return ErrorMsg{Error: fmt.Sprintf("Failed to create Kubernetes client: %v", err)}
		}

		m.k8sClient = clientset

		// List namespaces
		namespaces, err := clientset.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return ErrorMsg{Error: fmt.Sprintf("Failed to list namespaces: %v", err)}
		}

		nsNames := make([]string, len(namespaces.Items))
		for i, ns := range namespaces.Items {
			nsNames[i] = ns.Name
		}

		return NamespacesLoadedMsg{Namespaces: nsNames}
	}
}

// loadDeployments loads deployments for the selected namespace
func (m *KlogModel) loadDeployments() tea.Cmd {
	return func() tea.Msg {
		deployments, err := m.k8sClient.AppsV1().Deployments(m.selectedNamespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return ErrorMsg{Error: fmt.Sprintf("Failed to list deployments: %v", err)}
		}

		depNames := make([]string, len(deployments.Items))
		for i, dep := range deployments.Items {
			depNames[i] = dep.Name
		}

		return DeploymentsLoadedMsg{Deployments: depNames}
	}
}

// handleEscape handles the escape key for navigation
func (m *KlogModel) handleEscape() (tea.Model, tea.Cmd) {
	switch m.state {
	case StateContextSelection:
		return selectorModel{}, nil
	case StateNamespaceSelection:
		m.state = StateContextSelection
		m.cursor = 0
		return m, nil
	case StateDeploymentSelection:
		m.state = StateNamespaceSelection
		m.cursor = 0
		return m, nil
	case StateLogStreaming:
		if m.searchMode {
			m.searchMode = false
			m.searchInput.Blur()
			return m, nil
		}
		m.state = StateDeploymentSelection
		m.cursor = 0
		return m, nil
	case StateError:
		return selectorModel{}, nil
	}
	return m, nil
}

// updateContextSelection handles context selection state
func (m *KlogModel) updateContextSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		if m.cursor > 0 {
			m.cursor--
		}
	case "down", "j":
		if m.cursor < len(m.contexts)-1 {
			m.cursor++
		}
	case "enter":
		if len(m.contexts) > 0 {
			m.selectedContext = m.contexts[m.cursor]
			m.state = StateNamespaceSelection
			m.cursor = 0
			m.loading = true
			return m, m.loadNamespaces()
		}
	}
	return m, nil
}

// updateNamespaceSelection handles namespace selection state
func (m *KlogModel) updateNamespaceSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		if m.cursor > 0 {
			m.cursor--
		}
	case "down", "j":
		if m.cursor < len(m.namespaces)-1 {
			m.cursor++
		}
	case "enter":
		if len(m.namespaces) > 0 {
			m.selectedNamespace = m.namespaces[m.cursor]
			m.state = StateDeploymentSelection
			m.cursor = 0
			m.loading = true
			return m, m.loadDeployments()
		}
	}
	return m, nil
}

// updateDeploymentSelection handles deployment selection state
func (m *KlogModel) updateDeploymentSelection(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		if m.cursor > 0 {
			m.cursor--
		}
	case "down", "j":
		if m.cursor < len(m.deployments)-1 {
			m.cursor++
		}
	case "enter":
		if len(m.deployments) > 0 {
			m.selectedDeployment = m.deployments[m.cursor]
			m.state = StateLogStreaming
			m.cursor = 0
			// TODO: Start log streaming
			return m, m.startLogStreaming()
		}
	}
	return m, nil
}

// updateLogStreaming handles log streaming state
func (m *KlogModel) updateLogStreaming(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	if m.searchMode {
		switch msg.String() {
		case "enter":
			m.searchMode = false
			m.searchInput.Blur()
			// TODO: Perform search
			return m, nil
		case "esc":
			m.searchMode = false
			m.searchInput.Blur()
			return m, nil
		}
		return m, nil
	}

	switch msg.String() {
	case "/":
		m.searchMode = true
		m.searchInput.Focus()
		return m, nil
	case "q":
		return selectorModel{}, nil
	case "up", "k":
		if m.scrollOffset > 0 {
			m.scrollOffset--
		}
	case "down", "j":
		if m.scrollOffset < len(m.logLines)-1 {
			m.scrollOffset++
		}
	case "w":
		m.wrapMode = !m.wrapMode
	case "p":
		m.paused = !m.paused
	}
	return m, nil
}

// updateError handles error state
func (m *KlogModel) updateError(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "enter", "q":
		return selectorModel{}, nil
	}
	return m, nil
}

// startLogStreaming starts streaming logs from the selected deployment
func (m *KlogModel) startLogStreaming() tea.Cmd {
	return func() tea.Msg {
		// TODO: Implement actual log streaming
		// For now, just add some sample logs
		sampleLogs := []LogLine{
			{
				Timestamp: time.Now(),
				PodName:   m.selectedDeployment + "-pod-1",
				Content:   "Starting application...",
				Color:     lipgloss.Color("86"),
			},
			{
				Timestamp: time.Now(),
				PodName:   m.selectedDeployment + "-pod-2",
				Content:   "Database connection established",
				Color:     lipgloss.Color("212"),
			},
		}

		// This would be replaced with actual log streaming logic
		return LogsReceivedMsg{Lines: sampleLogs}
	}
}

type LogsReceivedMsg struct {
	Lines []LogLine
}

// viewContextSelection renders the context selection view
func (m *KlogModel) viewContextSelection() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		MarginBottom(1)

	itemStyle := lipgloss.NewStyle().PaddingLeft(2)
	selectedStyle := lipgloss.NewStyle().
		PaddingLeft(2).
		Foreground(lipgloss.Color("170")).
		Bold(true).
		Background(lipgloss.Color("240"))

	s := headerStyle.Render("🚢 Kubernetes Log Aggregator") + "\n\n"

	if m.loading {
		s += "Loading contexts...\n"
		return s
	}

	s += "Select a Kubernetes context:\n\n"

	for i, context := range m.contexts {
		cursor := " "
		if m.cursor == i {
			cursor = ">"
		}

		line := fmt.Sprintf("%s %s", cursor, context)
		if m.cursor == i {
			s += selectedStyle.Render(line) + "\n"
		} else {
			s += itemStyle.Render(line) + "\n"
		}
	}

	s += "\n" + lipgloss.NewStyle().Faint(true).Render("Press Enter to select, Esc to go back")
	return s
}

// viewNamespaceSelection renders the namespace selection view
func (m *KlogModel) viewNamespaceSelection() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		MarginBottom(1)

	itemStyle := lipgloss.NewStyle().PaddingLeft(2)
	selectedStyle := lipgloss.NewStyle().
		PaddingLeft(2).
		Foreground(lipgloss.Color("170")).
		Bold(true).
		Background(lipgloss.Color("240"))

	s := headerStyle.Render("🚢 Kubernetes Log Aggregator") + "\n\n"
	s += fmt.Sprintf("Context: %s\n", m.selectedContext)

	if m.loading {
		s += "Loading namespaces...\n"
		return s
	}

	s += "Select a namespace:\n\n"

	for i, namespace := range m.namespaces {
		cursor := " "
		if m.cursor == i {
			cursor = ">"
		}

		line := fmt.Sprintf("%s %s", cursor, namespace)
		if m.cursor == i {
			s += selectedStyle.Render(line) + "\n"
		} else {
			s += itemStyle.Render(line) + "\n"
		}
	}

	s += "\n" + lipgloss.NewStyle().Faint(true).Render("Press Enter to select, Esc to go back")
	return s
}

// viewDeploymentSelection renders the deployment selection view
func (m *KlogModel) viewDeploymentSelection() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		MarginBottom(1)

	itemStyle := lipgloss.NewStyle().PaddingLeft(2)
	selectedStyle := lipgloss.NewStyle().
		PaddingLeft(2).
		Foreground(lipgloss.Color("170")).
		Bold(true).
		Background(lipgloss.Color("240"))

	s := headerStyle.Render("🚢 Kubernetes Log Aggregator") + "\n\n"
	s += fmt.Sprintf("Context: %s\n", m.selectedContext)
	s += fmt.Sprintf("Namespace: %s\n", m.selectedNamespace)

	if m.loading {
		s += "Loading deployments...\n"
		return s
	}

	s += "Select a deployment:\n\n"

	for i, deployment := range m.deployments {
		cursor := " "
		if m.cursor == i {
			cursor = ">"
		}

		line := fmt.Sprintf("%s %s", cursor, deployment)
		if m.cursor == i {
			s += selectedStyle.Render(line) + "\n"
		} else {
			s += itemStyle.Render(line) + "\n"
		}
	}

	s += "\n" + lipgloss.NewStyle().Faint(true).Render("Press Enter to select, Esc to go back")
	return s
}

// viewLogStreaming renders the log streaming view
func (m *KlogModel) viewLogStreaming() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		MarginBottom(1)

	s := headerStyle.Render("🚢 Kubernetes Log Aggregator") + "\n\n"
	s += fmt.Sprintf("Context: %s | Namespace: %s | Deployment: %s\n",
		m.selectedContext, m.selectedNamespace, m.selectedDeployment)

	if m.paused {
		s += lipgloss.NewStyle().Foreground(lipgloss.Color("208")).Render("[PAUSED]") + " "
	}

	s += "\n"

	// Search bar
	if m.searchMode {
		s += "Search: " + m.searchInput.View() + "\n\n"
	} else {
		s += "Press / to search\n\n"
	}

	// Log lines
	logStyle := lipgloss.NewStyle().PaddingLeft(1)
	for i, line := range m.logLines {
		if i < m.scrollOffset {
			continue
		}

		prefix := fmt.Sprintf("[%s]", line.PodName)
		prefixStyle := lipgloss.NewStyle().Foreground(line.Color).Bold(true)

		logLine := prefixStyle.Render(prefix) + " " + line.Content
		s += logStyle.Render(logLine) + "\n"
	}

	// Help text
	helpStyle := lipgloss.NewStyle().Faint(true)
	s += "\n" + helpStyle.Render("Controls: / search, ↑↓ scroll, w wrap, p pause, q quit")

	return s
}

// viewError renders the error view
func (m *KlogModel) viewError() string {
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("196")).
		MarginBottom(1)

	errorStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("196")).
		Padding(1, 2).
		MarginTop(1).
		MarginBottom(1)

	s := headerStyle.Render("❌ Error") + "\n\n"
	s += errorStyle.Render(m.errorMsg) + "\n"
	s += lipgloss.NewStyle().Faint(true).Render("Press Enter or q to return to tool selection")

	return s
}